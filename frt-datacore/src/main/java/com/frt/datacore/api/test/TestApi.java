package com.frt.datacore.api.test;

import com.frt.datacore.common.utils.ValidateUtil;
import com.frt.datacore.domain.result.base.ValidateResult;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test")
public class TestApi {

    @Autowired
    private RedissonClient redissonClient;

    @GetMapping("/hello")
    public ValidateResult hello() {
        RBucket<String> redissonClientBucket = redissonClient.getBucket("test");
        redissonClientBucket.set("321");
        ValidateResult result = new ValidateResult();
        result.setMsg(redissonClientBucket.get());
        result.setResult(true);
        return result;
    }

    @GetMapping("/exception")
    public String exception() {
        throw ValidateUtil.validateMsg("123");
    }

    @GetMapping("/void")
    public void validateException() {}
}