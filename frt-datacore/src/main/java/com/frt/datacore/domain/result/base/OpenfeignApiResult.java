/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.datacore.domain.result.base;

import com.frt.datacore.common.exception.base.BaseException;
import lombok.Data;

/**
 * <AUTHOR>
 * @version OpenfeignApiResult.java, v 0.1 2025-08-27 13:51 wangyi
 */
@Data
public class OpenfeignApiResult {

    /**
     * 用于openfeign解码器识别业务异常使用
     */
    private boolean feignBusinessSuccess;

    /**
     * 状态码
     */
    private String code;

    /**
     * 子状态码
     */
    private String subCode;

    /**
     * 返回信息
     */
    private String message;

    /**
     * 子返回信息
     */
    private String subMessage;

    public static OpenfeignApiResult fail(String code, String msg, String subCode) {
        OpenfeignApiResult result = new OpenfeignApiResult();
        result.setFeignBusinessSuccess(Boolean.FALSE);
        result.setCode(code);
        result.setMessage(msg);
        result.setSubCode(subCode);
        return result;
    }

    public static OpenfeignApiResult fail(String code, String msg, String subCode, String subMessage) {
        OpenfeignApiResult result = fail(code, msg, subCode);
        result.setSubMessage(subMessage);
        return result;
    }

    public static OpenfeignApiResult fail(BaseException exception) {
        return fail(exception.getCode(), exception.getMsg(), exception.getSubCode());
    }
}