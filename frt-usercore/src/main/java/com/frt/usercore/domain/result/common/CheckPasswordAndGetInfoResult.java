/**
 * <AUTHOR>
 * @date 2025/9/2 15:37
 * @version 1.0 CheckPasswordAndGetInfoResult
 */
package com.frt.usercore.domain.result.common;

import com.frt.usercore.dao.entity.AccountDO;
import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @version CheckPasswordAndGetInfoResult.java, v 0.1 2025-09-02 15:37 tuyuwei
 */
@Data
public class CheckPasswordAndGetInfoResult {

    /**
     * 校验结果 true-成功 false-失败
     */
    private  Boolean success;

    /**
     * 密码错误次数
     */
    private  Integer passwordErrorCount;

    /**
     * 密码错误最大次数
     */
    private  Integer passwordMaxErrorCount;

    /**
     * 展示锁定时间（分钟）（nacos 获取）
     */
    private  Integer showLockTime;

    /**
     * 账号信息
     */
    AccountDO accountDO;
}