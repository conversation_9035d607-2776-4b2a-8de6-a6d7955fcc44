package com.frt.usercore.domain.param.operationadmin.rolemanager;

import lombok.Data;

import java.util.List;

/**
 * 新增角色参数
 *
 * <AUTHOR>
 * @version RoleAddParam.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class RoleAddParam {


    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 平台类型 1-运营后台 2-代理商 3-商户
     */
    private Integer platformType;

    /**
     * 菜单 id 列表
     */
    private List<String> menuIdList;
}
