package com.frt.usercore.domain.result.operationadmin.rolemanager;

import lombok.Data;

/**
 * 角色列表查询结果
 *
 * <AUTHOR>
 * @version RoleListQueryResult.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class RoleListQueryResult {

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色状态
     */
    private Integer roleStatus;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 关联账号数量
     */
    private Integer linkedAccounts;

}
