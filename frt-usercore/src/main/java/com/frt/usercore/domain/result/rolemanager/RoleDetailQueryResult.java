package com.frt.usercore.domain.result.rolemanager;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 角色详情查询结果
 *
 * <AUTHOR>
 * @version RoleDetailQueryResult.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleDetailQueryResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 2201039545936282708L;
    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色模版ID
     */
    private String roleTemplateId;

    /**
     * 角色模版名称
     */
    private String roleTemplateName;


    /**
     * 菜单 id 列表
     */
    private List<String> menuIdList;

    /**
     * 商户后台角色权限列表
     */
    private MerchantRolePermissionResult adminMenu;

    /**
     * 商户后台角色权限列表
     */
    private MerchantRolePermissionResult minaMenu;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 权限列表
     */
    private List<String> permissionList;
    /**
     * 描述
     */
    private String remark;
}