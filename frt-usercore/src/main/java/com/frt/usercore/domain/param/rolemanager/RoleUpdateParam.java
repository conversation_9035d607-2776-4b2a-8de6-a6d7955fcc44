package com.frt.usercore.domain.param.rolemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 角色更新参数
 *
 * <AUTHOR>
 * @version RoleUpdateParam.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleUpdateParam implements Serializable {

    private static final long serialVersionUID = 5311686757375806690L;
    /**
     * 角色ID
     */
    @NotBlank(message = "角色ID不能为空")
    private String roleId;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    /**
     * 角色模板ID
     */
    private String roleTemplateId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 权限列表
     */
    private List<String> menuList;

    private List<String> permissionList;
    /**
     * 平台类型 1-运营后台 2-代理商 3-商户
     */
    @NotNull(message = "平台类型不能为空")
    private Integer platformType;
}