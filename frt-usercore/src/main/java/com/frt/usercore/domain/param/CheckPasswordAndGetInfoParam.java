/**
 * <AUTHOR>
 * @date 2025/9/2 15:32
 * @version 1.0 CheckPasswordAndGetInfoParam
 */
package com.frt.usercore.domain.param;

import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @version CheckPasswordAndGetInfoParam.java, v 0.1 2025-09-02 15:32 tuyuwei
 */
@Data
public class CheckPasswordAndGetInfoParam {

    /**
     * 账号
     */
    private String account;
    /**
     * 密码
     */
    private String password;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 验证码
     */
    private String code;

    /**
     * 登录方式 1-密码 2-手机验证码
     */
    private Integer type;

    /**
     * 是否删除验证码
     */
    private Boolean isDeleteCode;
}