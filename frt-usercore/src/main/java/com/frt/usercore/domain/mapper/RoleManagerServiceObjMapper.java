/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.mapper;

import com.frt.usercore.dao.entity.TenantRoleDO;
import com.frt.usercore.domain.result.rolemanager.RoleInfoResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @version RoleManagerServiceObjMapper.java, v 0.1 2025-08-28 21:15 zhangling
 */
@Mapper(componentModel = "spring")
public interface RoleManagerServiceObjMapper {

    @Mappings( {
            @Mapping(source = "roleDescription", target = "remark")
    })
    List<RoleInfoResult> toRoleInfoResultList(List<TenantRoleDO> list);
}