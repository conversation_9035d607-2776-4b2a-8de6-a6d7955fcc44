/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 员工更新参数
 *
 * <AUTHOR>
 * @version UserUpdateParam.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserUpdateParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;
    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private String merchantId;

    /**
     * 员工ID
     */
    @NotBlank(message = "员工ID不能为空")
    private String userId;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 员工手机号
     */
    private String phone;

    /**
     * 角色ID
     */
    @NotBlank(message = "角色ID不能为空")
    private String roleId;

    /**
     * 门店ID
     */
    private List<String> storeIdList;
}