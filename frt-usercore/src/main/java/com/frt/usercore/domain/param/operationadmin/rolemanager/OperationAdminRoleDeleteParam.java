package com.frt.usercore.domain.param.operationadmin.rolemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * 角色删除参数
 *
 * <AUTHOR>
 * @version RoleDeleteParam.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class OperationAdminRoleDeleteParam implements Serializable {

    @Serial
    private static final long serialVersionUID = -2365126329205820300L;

    /**
     * 角色ID
     */
    @NotBlank(message = "角色ID不能为空")
    private String roleId;

}