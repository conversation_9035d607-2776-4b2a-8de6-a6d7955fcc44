package com.frt.usercore.domain.param.operationadmin.usermanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 修改员工参数
 *
 * <AUTHOR>
 * @version UserModifyParam.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class UserModifyParam {

    /**
     * 员工ID
     */
    @NotBlank(message = "员工ID不能为空")
    private String userId;

    /**
     * 员工账号
     */
    @NotBlank(message = "员工账号不能为空")
    private String username;

    /**
     * 密码（MD5）
     */
    private String password;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 状态 1-正常 2-禁用 3-注销
     */
    private Integer accountStatus;
}
