package com.frt.usercore.domain.param.storemanager;

import lombok.Data;

import java.util.List;

@Data
public class StoreListQueryParam {

    /**
     * 数据来源
     */
    private String source;
    /**
     * 门店状态，门店是否展示 SHOW-展示 HIDE-隐藏
     */
    private String isShow;
    /**
     * 门店ID
     */
    private String storeId;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店ID
     */
    private List<String> storeIdList;
    /**
     * 门店名称
     */
    private List<String> storeNameList;
}
