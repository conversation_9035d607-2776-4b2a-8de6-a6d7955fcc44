package com.frt.usercore.dao.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.common.enums.business.DelFlagEnum;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.MerchantUserStoreDO;
import com.frt.usercore.dao.mapper.MerchantStoreInfoMapper;
import com.frt.usercore.dao.mapper.MerchantUserStoreMapper;
import com.frt.usercore.dao.repository.MerchantUserStoreDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商户员工门店关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class MerchantUserStoreDAOImpl extends ServiceImpl<MerchantUserStoreMapper, MerchantUserStoreDO> implements MerchantUserStoreDAO {

    @Autowired
    private MerchantStoreInfoMapper merchantStoreInfoMapper;

    @Override
    public List<MerchantUserStoreDO> findByUserIdAndMerchantId(String userId, String merchantId) {
        return this.lambdaQuery()
                .eq(MerchantUserStoreDO::getUserId, userId)
                .eq(MerchantUserStoreDO::getMerchantId, merchantId)
                .eq(MerchantUserStoreDO::getIsDel, 0)
                .list();
    }

    @Override
    public List<MerchantUserStoreDO> findByUserIdAndMerchantIdAndStoreId(String userId, String merchantId, List<String> storeIdList) {
        if (StrUtil.isBlank(userId)
                && StrUtil.isBlank(merchantId)
                && CollectionUtil.isNotEmpty(storeIdList)) {
            throw ValidateUtil.validateMsg("参数不能为空");
        }
        return query()
                .eq(StrUtil.isNotBlank(userId), MerchantUserStoreDO.USER_ID, userId)
                .eq(StrUtil.isNotBlank(merchantId), MerchantUserStoreDO.MERCHANT_ID, merchantId)
                .in(CollectionUtil.isNotEmpty(storeIdList), MerchantUserStoreDO.STORE_ID, storeIdList)
                .list();
    }

    /**
     * 根据用户ID删除
     *
     * @param userId
     */
    @Override
    public void removeByUserId(String userId) {
        update()
                .set(MerchantUserStoreDO.IS_DEL, DelFlagEnum.DELETED.getCode())
                .eq(MerchantUserStoreDO.USER_ID, userId)
                .update();
    }


}
