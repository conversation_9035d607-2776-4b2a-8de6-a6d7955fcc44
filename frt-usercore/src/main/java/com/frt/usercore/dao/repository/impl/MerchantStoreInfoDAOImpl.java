package com.frt.usercore.dao.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.common.enums.business.StoreStatusEnum;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.frt.usercore.dao.mapper.MerchantStoreInfoMapper;
import com.frt.usercore.dao.repository.MerchantStoreInfoDAO;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.UserStoreListParam;
import com.frt.usercore.domain.param.storemanager.StoreListQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 门店信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
public class MerchantStoreInfoDAOImpl extends ServiceImpl<MerchantStoreInfoMapper, MerchantStoreInfoDO> implements MerchantStoreInfoDAO {

    @Override
    public List<MerchantStoreInfoDO> findByStoreIdListAndMerchantId(List<String> storeIdList, String merchantId) {
        return query()
                .in(MerchantStoreInfoDO.STORE_ID, storeIdList)
                .eq(MerchantStoreInfoDO.MERCHANT_ID, merchantId)
               .list();
    }

    @Override
    public Page<MerchantStoreInfoDO> findStorePageWithTenantId(PageParam<StoreListQueryParam> param) {
        return query()
                .eq(MerchantStoreInfoDO.TENANT_ID, TenantContextUtil.getTenantId())
                .eq(MerchantStoreInfoDO.MERCHANT_ID, TenantContextUtil.getMerchantId())
                .eq(StrUtil.isNotBlank(param.getQuery().getIsShow()), MerchantStoreInfoDO.IS_SHOW, param.getQuery().getIsShow())
                .eq(StrUtil.isNotBlank(param.getQuery().getStoreId()), MerchantStoreInfoDO.STORE_ID, param.getQuery().getStoreId())
                .likeRight(StrUtil.isNotBlank(param.getQuery().getStoreName()), MerchantStoreInfoDO.STORE_NAME, param.getQuery().getStoreName())
                .in(CollectionUtil.isNotEmpty(param.getQuery().getStoreIdList()), MerchantStoreInfoDO.STORE_ID, param.getQuery().getStoreIdList())
                .in(CollectionUtil.isNotEmpty(param.getQuery().getStoreNameList()), MerchantStoreInfoDO.STORE_NAME, param.getQuery().getStoreNameList())
                .orderByDesc(MerchantStoreInfoDO.CREATE_TIME)
                .page(Page.of(param.getPage(), param.getPageSize()));
    }

    @Override
    public MerchantStoreInfoDO getInfoByStoreIdWithTenantId(String storeId) {
        return query()
                .eq(MerchantStoreInfoDO.TENANT_ID, TenantContextUtil.getTenantId())
                .eq(MerchantStoreInfoDO.STORE_ID, storeId)
                .last("limit 1")
                .one();
    }

    @Override
    public MerchantStoreInfoDO getInfoByStoreName(String storeName) {
        return query()
                .eq(MerchantStoreInfoDO.STORE_NAME, storeName)
                .last("limit 1")
                .one();
    }

    @Override
    public void updateByStoreIdAndMerchantIdWithTenantId(MerchantStoreInfoDO storeInfoDO) {
        update()
                .eq(MerchantStoreInfoDO.STORE_ID, storeInfoDO.getStoreId())
                .eq(MerchantStoreInfoDO.TENANT_ID, TenantContextUtil.getTenantId())
                .eq(MerchantStoreInfoDO.MERCHANT_ID, storeInfoDO.getMerchantId())
                .update(storeInfoDO);
    }

    @Override
    public List<MerchantStoreInfoDO> findByStoreIdListAndMerchantIdAndStoreName(List<String> storeIdList, String merchantId, String storeName) {
        return query()
                .in(CollectionUtil.isNotEmpty(storeIdList), MerchantStoreInfoDO.STORE_ID, storeIdList)
                .eq(StrUtil.isNotBlank(merchantId), MerchantStoreInfoDO.MERCHANT_ID, merchantId)
                .eq(StrUtil.isNotBlank(storeName), MerchantStoreInfoDO.STORE_NAME, storeName)
                .list();
    }

    @Override
    public Page<MerchantStoreInfoDO> findStoreListPageWithTenantId(PageParam<UserStoreListParam> param) {
        return query()
                .eq(MerchantStoreInfoDO.TENANT_ID, TenantContextUtil.getTenantId())
                .eq(MerchantStoreInfoDO.MERCHANT_ID, TenantContextUtil.getMerchantId())
                .likeRight(StrUtil.isNotBlank(param.getQuery().getStoreName()), MerchantStoreInfoDO.STORE_NAME, param.getQuery().getStoreName())
                .eq(StrUtil.isNotBlank(param.getQuery().getIsShow()), MerchantStoreInfoDO.IS_SHOW, StoreStatusEnum.SHOW.getCode())
                .orderByDesc(MerchantStoreInfoDO.CREATE_TIME)
                .page(Page.of(param.getPage(), param.getPageSize()));
    }
}
