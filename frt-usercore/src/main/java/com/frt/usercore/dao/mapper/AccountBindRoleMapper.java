package com.frt.usercore.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.frt.usercore.dao.entity.AccountBindRoleDO;
import com.frt.usercore.domain.dto.result.RoleLinkAccountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 账号角色关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface AccountBindRoleMapper extends BaseMapper<AccountBindRoleDO> {

    boolean updateRoleIdByUserId(AccountBindRoleDO accountBindRoleDO);

    List<RoleLinkAccountDTO> countRoleLinkAccount(@Param("roleIdList") List<String> userIdList);

}
