package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.AccountBindRoleDO;
import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.frt.usercore.dao.mapper.AccountBindRoleMapper;
import com.frt.usercore.dao.repository.AccountBindRoleDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.domain.dto.result.RoleLinkAccountDTO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 账号角色关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class AccountBindRoleDAOImpl extends ServiceImpl<AccountBindRoleMapper, AccountBindRoleDO> implements AccountBindRoleDAO {

    @Override
    public void updateRoleIdByUserId(AccountBindRoleDO accountBindRoleDO) {
        baseMapper.updateRoleIdByUserId(accountBindRoleDO);
    }

    /**
     * 根据用户id查询
     *
     * @param userId
     * @return
     */
    @Override
    public AccountBindRoleDO getByUserId(String userId) {
        return this.lambdaQuery()
                .eq(AccountBindRoleDO::getUserId, userId)
                .eq(AccountBindRoleDO::getIsDel, 0)
                .last("limit 1")
                .one();
    }

    /**
     * 根据角色id查询
     *
     * @param roleId
     * @return
     */
    @Override
    public AccountBindRoleDO getOneByRoleId(String roleId) {
        return this.lambdaQuery()
                .eq(AccountBindRoleDO::getRoleId, roleId)
                .eq(AccountBindRoleDO::getIsDel, 0)
                .last("limit 1")
                .one();
    }

    /**
     * 根据用户id列表查询角色数量
     *
     * @param userIdList
     * @return
     */
    @Override
    public List<RoleLinkAccountDTO> countRoleLinkAccount(List<String> userIdList) {
        return getBaseMapper().countRoleLinkAccount(userIdList);
    }
}
