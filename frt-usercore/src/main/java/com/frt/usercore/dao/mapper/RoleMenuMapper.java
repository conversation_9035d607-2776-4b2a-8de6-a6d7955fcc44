package com.frt.usercore.dao.mapper;

import com.frt.usercore.dao.entity.RoleMenuDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色菜单关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface RoleMenuMapper extends BaseMapper<RoleMenuDO> {

	/**
	 * 根据角色ID查询菜单ID列表
	 * @param roleId 角色ID
	 * @return 菜单ID列表
	 */
	List<String> findMenuIdListByRoleId(@Param("roleId") String roleId);
}
