package com.frt.usercore.dao.repository.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.dao.entity.GaodeCodeDO;
import com.frt.usercore.dao.mapper.GaodeCodeMapper;
import com.frt.usercore.dao.repository.GaodeCodeDAO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 地址信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
public class GaodeCodeDAOImpl extends ServiceImpl<GaodeCodeMapper, GaodeCodeDO> implements GaodeCodeDAO {

    @Override
    public List<GaodeCodeDO> findListByCodeAndLevel(String provinceCode, String cityCode, Integer level) {
        return query()
                .eq(StrUtil.isNotBlank(provinceCode), GaodeCodeDO.PROVINCE, provinceCode)
                .eq(StrUtil.isNotBlank(cityCode), GaodeCodeDO.CITY, cityCode)
                .eq(GaodeCodeDO.LEVEL, level)
                .list();
    }

    @Override
    public GaodeCodeDO getInfoByCode(String code) {
        return query()
                .eq(GaodeCodeDO.CODE, code)
                .last("limit 1")
                .one();
    }

    @Override
    public List<GaodeCodeDO> findAll() {
        return query()
                .list();
    }
}
