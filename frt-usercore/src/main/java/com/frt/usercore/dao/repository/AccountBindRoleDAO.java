package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.AccountBindRoleDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.frt.usercore.domain.dto.result.RoleLinkAccountDTO;

import java.util.List;

/**
 * <p>
 * 账号角色关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface AccountBindRoleDAO extends IService<AccountBindRoleDO> {

    void updateRoleIdByUserId(AccountBindRoleDO accountBindRoleDO);

    /**
     * 根据用户id查询
     * @param userId
     * @return
     */
    AccountBindRoleDO getByUserId(String userId);

    /**
     * 根据角色id查询
     * @param roleId
     * @return
     */
    AccountBindRoleDO getOneByRoleId(String roleId);

    /**
     * 根据用户id列表查询角色数量
     * @param userIdList
     * @return
     */
    List<RoleLinkAccountDTO> countRoleLinkAccount(List<String> userIdList);
}
