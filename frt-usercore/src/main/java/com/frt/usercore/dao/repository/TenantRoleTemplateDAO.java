package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.TenantRoleTemplateDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 基础权限配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
public interface TenantRoleTemplateDAO extends IService<TenantRoleTemplateDO> {

    /**
     *
     * @param tenantId
     * @param templateId
     * @param platformType
     * @return
     */
    TenantRoleTemplateDO getByTenantIdAndTemplateIdAndPlatformType(String tenantId, String templateId, Integer platformType);

    /**
     * 根据租户id和平台类型查询
     * @param tenantId
     * @param platformType
     * @return
     */
    List<TenantRoleTemplateDO> getByTenantIdAndPlatformType(String tenantId, Integer platformType);

    /**
     * 根据租户id和模板id列表查询
     *
     * @param tenantId
     * @param templateIdList
     * @return
     */
    List<TenantRoleTemplateDO> selectByTenantIdAndTemplateIdList(String tenantId, List<String> templateIdList);

    /**
     * 根据租户id和模板id查询
     *
     * @param tenantId
     * @param templateId
     * @return
     */
    TenantRoleTemplateDO getByTenantIdAndTemplateId(String tenantId, String templateId);
}
