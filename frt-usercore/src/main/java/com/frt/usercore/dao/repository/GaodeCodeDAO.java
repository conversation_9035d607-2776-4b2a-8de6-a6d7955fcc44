package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.GaodeCodeDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.frt.usercore.domain.param.common.CommonAddressCodeListQueryParam;

import java.util.List;

/**
 * <p>
 * 地址信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
public interface GaodeCodeDAO extends IService<GaodeCodeDO> {

    /**
     * 根据code和level查询地址信息
     * @param provinceCode
     * @param cityCode
     * @param level
     * @return
     */
    List<GaodeCodeDO> findListByCodeAndLevel(String provinceCode, String cityCode, Integer level);

    /**
     * 根据code查询地址信息
     * @param code
     * @return
     */
    GaodeCodeDO getInfoByCode(String code);

    /**
     * 查询所有地址信息
     * @return
     */
    List<GaodeCodeDO> findAll();

}
