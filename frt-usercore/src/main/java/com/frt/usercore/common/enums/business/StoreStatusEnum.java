package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 账号状态类型枚举
 */
@Getter
public enum StoreStatusEnum {

    /**
     * 展示
     */
    SHOW("SHOW", "展示"),

    /**
     * 隐藏
     */
    HIDE("HIDE", "隐藏"),
    ;

    private final String code;
    private final String description;

    StoreStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static StoreStatusEnum getByCode(String code) {
        for (StoreStatusEnum value : StoreStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 校验值是否合法
     */
    public static boolean validate(String value) {
        return getByCode(value) != null;
    }
}