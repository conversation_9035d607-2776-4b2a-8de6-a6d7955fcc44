package com.frt.usercore.service.impl.common;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.frt.usercore.common.constants.CommonConstant;
import com.frt.usercore.common.enums.business.PlatformSourceEnum;
import com.frt.usercore.dao.entity.GaodeCodeDO;
import com.frt.usercore.dao.entity.UnityCategoryDO;
import com.frt.usercore.dao.repository.GaodeCodeDAO;
import com.frt.usercore.dao.repository.UnityCategoryDAO;
import com.frt.usercore.domain.mapper.CommonMapper;
import com.frt.usercore.domain.param.common.CommonAddressCodeListQueryParam;
import com.frt.usercore.domain.param.common.CommonUnityCategoryListQueryParam;
import com.frt.usercore.domain.result.common.CommonAddressCodeListQueryResult;
import com.frt.usercore.domain.result.common.CommonUnityCategoryListQueryResult;
import com.frt.usercore.service.common.CommonService;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommonServiceImpl implements CommonService {

    private final GaodeCodeDAO gaodeCodeDAO;
    private final UnityCategoryDAO unityCategoryDAO;
    private final CommonMapper commonMapper;

    @Override
    public CommonAddressCodeListQueryResult queryAddressCodeList(CommonAddressCodeListQueryParam param) {

        // 商户后台需要查询所有的地址数据
        if (StrUtil.equalsIgnoreCase(PlatformSourceEnum.MERCHANT_ADMIN.getCode(), param.getSource())) {
            List<GaodeCodeDO> gaodeCodeDOList = gaodeCodeDAO.findAll();

            // 省市区列表分离
            List<GaodeCodeDO> provinceDOList = gaodeCodeDOList.stream().filter(item -> item.getLevel() == 0).collect(Collectors.toList());
            List<GaodeCodeDO> cityDOList = gaodeCodeDOList.stream().filter(item -> item.getLevel() == 1).collect(Collectors.toList());
            List<GaodeCodeDO> areaDOList = gaodeCodeDOList.stream().filter(item -> item.getLevel() == 2).collect(Collectors.toList());

            List<CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult> provinceList = commonMapper.coverGaodeCodeDOToCommonAddressCodeInfoQueryResult(provinceDOList);
            List<CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult> cityList = commonMapper.coverGaodeCodeDOToCommonAddressCodeInfoQueryResult(cityDOList);
            List<CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult> areaList = commonMapper.coverGaodeCodeDOToCommonAddressCodeInfoQueryResult(areaDOList);

            // Map<province,cityList>
            Map<String, List<CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult>> cityMap = cityList.stream().collect(Collectors.groupingBy(CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult::getProvince));
            // Map<city,areaList>
            Map<String, List<CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult>> areaMap = areaList.stream().collect(Collectors.groupingBy(CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult::getCity));

            cityList.forEach(city -> {
                List<CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult> areaModelList = areaMap.getOrDefault(city.getCode(), com.google.common.collect.Lists.newArrayList());
                city.setChildren(areaModelList);
            });

            provinceList.forEach(province -> {
                List<CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult> cityModelList = cityMap.getOrDefault(province.getCode(), com.google.common.collect.Lists.newArrayList());
                province.setChildren(cityModelList);
            });


            CommonAddressCodeListQueryResult result = new CommonAddressCodeListQueryResult();
            result.setList(provinceList);
            return result;
        }

        // 小程序的根据条件查询
        List<GaodeCodeDO> gaodeCodeDOList = gaodeCodeDAO.findListByCodeAndLevel(param.getProvinceCode(), param.getCityCode(), this.getLevel(param));
        List<CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(gaodeCodeDOList)) {
            list = gaodeCodeDOList.stream().map(item ->
                            CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult.init(item.getCode(), item.getName(), item.getNameFirstLetter()))
                    .collect(Collectors.toList());
        }

        return CommonAddressCodeListQueryResult.init(
                getGaoDeNameByCode(param.getProvinceCode()).get(CommonConstant.NAME_STR),
                getGaoDeNameByCode(param.getProvinceCode()).get(CommonConstant.CODE_STR),
                getGaoDeNameByCode(param.getProvinceCode()).get(CommonConstant.FIRST_LETTER_STR),
                getGaoDeNameByCode(param.getCityCode()).get(CommonConstant.NAME_STR),
                getGaoDeNameByCode(param.getCityCode()).get(CommonConstant.CODE_STR),
                getGaoDeNameByCode(param.getCityCode()).get(CommonConstant.FIRST_LETTER_STR),
                list
        );
    }

    @Override
    public CommonUnityCategoryListQueryResult queryUnityCategoryList(CommonUnityCategoryListQueryParam param) {

        // 查询1级类目
        List<UnityCategoryDO> firstList = unityCategoryDAO.findListByLevel(1);

        // 查询2级类目
        List<UnityCategoryDO> secondList = unityCategoryDAO.findListByLevel(2);
        Map<Integer, List<UnityCategoryDO>> secondListMap = secondList.stream()
                .collect(Collectors.groupingBy(UnityCategoryDO::getParentId));

        // 封装结果
        List<CommonUnityCategoryListQueryResult.CommonUnityCategoryInfoQueryResult> resultList = Lists.newArrayList();
        for (UnityCategoryDO firstModel : firstList) {
            CommonUnityCategoryListQueryResult.CommonUnityCategoryInfoQueryResult firstInfo = commonMapper.changeToUnityCategoryInfoQueryResult(firstModel);
            List<UnityCategoryDO> secondInfoDOList = secondListMap.get(firstModel.getId());
            List<CommonUnityCategoryListQueryResult.CommonUnityCategoryInfoQueryResult> secondInfoList = commonMapper.changeToUnityCategoryInfoListQueryResult(secondInfoDOList);
            firstInfo.setChildren(CollectionUtil.isNotEmpty(secondInfoList) ? secondInfoList : Lists.newArrayList());
            resultList.add(firstInfo);
        }

        CommonUnityCategoryListQueryResult result = new CommonUnityCategoryListQueryResult();
        result.setList(resultList);
        return result;
    }

    /**
     * 根据code获取信息
     */
    private Map<String, String> getGaoDeNameByCode(String code) {
        Map<String, String> map = Maps.newHashMap();
        map.put(CommonConstant.CODE_STR, StrUtil.EMPTY);
        map.put(CommonConstant.NAME_STR, StrUtil.EMPTY);
        map.put(CommonConstant.FIRST_LETTER_STR, StrUtil.EMPTY);
        if (StringUtils.isBlank(code)) {
            return map;
        }
        GaodeCodeDO gaodeCodeDO = new GaodeCodeDO();
        if (!StrUtil.equalsIgnoreCase(CommonConstant.ZERO_STR, code)) {
            gaodeCodeDO = gaodeCodeDAO.getInfoByCode(code);
        }
        map.put(CommonConstant.NAME_STR, ObjectUtil.isNotNull(gaodeCodeDO) && StringUtils.isNotBlank(gaodeCodeDO.getName()) ? gaodeCodeDO.getName() : StrUtil.EMPTY);
        map.put(CommonConstant.CODE_STR, ObjectUtil.isNotNull(gaodeCodeDO) && StringUtils.isNotBlank(gaodeCodeDO.getCode()) ? gaodeCodeDO.getCode() : StrUtil.EMPTY);
        map.put(CommonConstant.FIRST_LETTER_STR, ObjectUtil.isNotNull(gaodeCodeDO) && StringUtils.isNotBlank(gaodeCodeDO.getNameFirstLetter()) ? gaodeCodeDO.getNameFirstLetter() : StrUtil.EMPTY);
        return map;
    }


    /**
     * 获取等级
     *
     * @param param
     * @return
     */
    private Integer getLevel(CommonAddressCodeListQueryParam param) {
        int level = 0;
        if (StringUtils.isNotBlank(param.getProvinceCode())) {
            level = 1;
        }
        if (StringUtils.isNotBlank(param.getCityCode())) {
            level = 2;
        }
        return level;
    }

}
