/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service;

import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.rolemanager.*;
import com.frt.usercore.domain.result.ListResult;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.common.CommonResult;
import com.frt.usercore.domain.result.rolemanager.RoleDetailQueryResult;
import com.frt.usercore.domain.result.rolemanager.RoleInfoResult;
import com.frt.usercore.domain.result.rolemanager.RoleNameCheckResult;
import com.frt.usercore.domain.result.rolemanager.RoleTemplateInfoResult;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version RoleManagerService.java, v 0.1 2025-08-27 16:34 zhangling
 */
public interface RoleManagerService {
    /**
     * 查询角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    PageResult<RoleInfoResult> getRoleList(PageParam<RoleListQueryParam> param);

    /**
     * 查询角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    RoleDetailQueryResult getRoleDetail(RoleDetailQueryParam param);

    /**
     * 添加角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    CommonResult<Void> addRole(RoleAddParam param);

    /**
     * 更新角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    CommonResult<Void> updateRole(RoleUpdateParam param);

    /**
     * 删除角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    CommonResult<Void> deleteRole(RoleDeleteParam param);

    /**
     * 获取角色模板列表
     * @param param
     * @return
     */
    ListResult<RoleTemplateInfoResult> getRoleTemplateList(MerchantRoleTemplateParam param);

    /**
     * 角色名称检查
     * @param param
     * @return
     */
    RoleNameCheckResult checkRoleName(RoleNameCheckParam param);
}