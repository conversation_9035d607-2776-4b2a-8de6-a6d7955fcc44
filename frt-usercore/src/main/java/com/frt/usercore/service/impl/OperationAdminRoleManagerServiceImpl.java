package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.common.enums.business.BusinessIdEnum;
import com.frt.usercore.common.enums.business.RoleTypeEnum;
import com.frt.usercore.common.utils.IdWorkerUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.RoleMenuDO;
import com.frt.usercore.dao.entity.TenantRoleDO;
import com.frt.usercore.dao.repository.AccountBindRoleDAO;
import com.frt.usercore.dao.repository.RoleMenuDAO;
import com.frt.usercore.dao.repository.TenantRoleDAO;
import com.frt.usercore.domain.dto.param.RoleListQueryParamDTO;
import com.frt.usercore.domain.dto.result.RoleLinkAccountDTO;
import com.frt.usercore.domain.mapper.OperationAdminRoleManagerServiceObjMapper;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.CheckOperationAdminRoleParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.OperationAdminRoleDeleteParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleAddParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleDetailQueryParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleListQueryParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleModifyParam;
import com.frt.usercore.domain.param.rolemanager.RoleDeleteParam;
import com.frt.usercore.domain.param.rolemanager.RoleUpdateParam;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.operationadmin.rolemanager.CheckOperationAdminRoleResult;
import com.frt.usercore.domain.result.operationadmin.rolemanager.RoleDetailQueryResult;
import com.frt.usercore.domain.result.operationadmin.rolemanager.RoleListQueryResult;
import com.frt.usercore.service.OperationAdminRoleManagerService;
import com.frt.usercore.service.RoleManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 运营后台角色管理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationAdminRoleManagerServiceImpl implements OperationAdminRoleManagerService {


    @Autowired
    private TenantRoleDAO tenantRoleDAO;
    @Autowired
    private OperationAdminRoleManagerServiceObjMapper mapper;
    @Autowired
    private RoleMenuDAO roleMenuDAO;
	@Autowired
	private TransactionTemplate transactionTemplate;
	@Autowired
	private RoleManagerService roleManagerService;
	@Autowired
	private AccountBindRoleDAO accountBindRoleDAO;

    /**
     * 角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @Override
    public PageResult<RoleListQueryResult> getRoleList(PageParam<RoleListQueryParam> param) {
        PageParam<RoleListQueryParamDTO> pageDTO = new PageParam<>(param.getPage(), param.getPageSize());
        // 查询条件组装
        RoleListQueryParam queryParam = param.getQuery();
        RoleListQueryParamDTO queryDTO = new RoleListQueryParamDTO();
        queryDTO.setTenantId(TenantContextUtil.getTenantId());
        queryDTO.setRoleName(queryParam.getRoleName());
        queryDTO.setTerminalType(TenantContextUtil.getPlatformType().getCode());
        pageDTO.setQuery(queryDTO);
        Page<TenantRoleDO> pageList = tenantRoleDAO.findPageList(pageDTO);
        PageResult<RoleListQueryResult> resultPageResult = mapper.toRoleListQueryResultPageResult(pageList);
        List<RoleListQueryResult> records = resultPageResult.getRecords();
        // 使用 stream 获取角色 id 列表
        List<String> roleIdList = records.stream().map(RoleListQueryResult::getRoleId).toList();
        // 根据角色 id 列表查询角色是否关联用户
        final List<RoleLinkAccountDTO> roleLinkAccountDTOS = accountBindRoleDAO.countRoleLinkAccount(roleIdList);
        final Map<String, Integer> linkedAccountMap = CollectionUtil.emptyIfNull(roleLinkAccountDTOS).stream().collect(Collectors.toMap(RoleLinkAccountDTO::getRoleId, RoleLinkAccountDTO::getLinkedAccountCount));
        for (RoleListQueryResult record : records) {
            record.setLinkedAccounts(linkedAccountMap.getOrDefault(record.getRoleId(), 0));
        }
        return resultPageResult;
    }

    /**
     * 新增角色
     *
     * @param param 请求参数
     */
    @Override
    public void addRole(RoleAddParam param) {
        String tenantId = TenantContextUtil.getTenantId();
        com.frt.usercore.domain.param.rolemanager.RoleAddParam roleAddParam = new com.frt.usercore.domain.param.rolemanager.RoleAddParam();

        roleAddParam.setRoleName(param.getRoleName());
        roleAddParam.setPlatformType(TenantContextUtil.getPlatformType().getCode());
        roleAddParam.setMenuIdList(param.getMenuIdList());
        roleManagerService.addRole(roleAddParam);
        // TenantRoleDO tenantRoleDO = tenantRoleDAO.getByRoleName(param.getRoleName(), tenantId, param.getTerminalType(),null);
        // if (null != tenantRoleDO){
        //     throw ValidateUtil.validateMsg("该角色名称已重复，请修改后重新提交");
        // }
        //
        // TenantRoleDO insertDO = new TenantRoleDO();
        // insertDO.setRoleId(BusinessIdEnum.ROLE_ID.generateId());
        // insertDO.setTenantId(tenantId);
        // insertDO.setRoleName(param.getRoleName());
        // insertDO.setRoleType(RoleTypeEnum.OPERATION.getCode());
        // List<String> menuIdList = param.getMenuIdList();
        // List<RoleMenuDO> roleMenuDOList = new ArrayList<>();
        // for (String menuId : menuIdList) {
        //     RoleMenuDO roleMenuDO = new RoleMenuDO();
        //     roleMenuDO.setRoleId(insertDO.getRoleId());
        //     roleMenuDO.setTenantId(tenantId);
        //     roleMenuDO.setMenuId(menuId);
        //     roleMenuDOList.add(roleMenuDO);
        // }
        // // TODO YXR 2025/8/28 操作人
        // // insertDO.setCreatedBy();
        // // 事物
        // transactionTemplate.execute(status -> {
        //     tenantRoleDAO.save(insertDO);
        //     roleMenuDAO.saveBatch(roleMenuDOList);
        //     return Boolean.TRUE;
        // });

    }

    /**
     * 修改角色
     *
     * @param param 请求参数
     */
    @Override
    public void modifyRole(RoleModifyParam param) {
        String tenantId = TenantContextUtil.getTenantId();
        RoleUpdateParam roleUpdateParam = new RoleUpdateParam();
        roleUpdateParam.setRoleId(param.getRoleId());
        roleUpdateParam.setRoleName(param.getRoleName());
        roleUpdateParam.setMenuList(param.getMenuIdList());
        roleUpdateParam.setPlatformType(TenantContextUtil.getPlatformType().getCode());
        roleManagerService.updateRole(roleUpdateParam);
        //
        // // 校验角色是否存在
        // TenantRoleDO existRole = tenantRoleDAO.getByRoleId(param.getRoleId());
        // if (existRole == null) {
        //     throw ValidateUtil.validateMsg("角色不存在");
        // }
        //
        // // 校验角色名称是否重复（排除当前角色）
        // TenantRoleDO duplicateRole = tenantRoleDAO.getByRoleName(param.getRoleName(), tenantId, TenantContextUtil.getPlatformType().getCode(), param.getRoleId());
        // if (duplicateRole != null) {
        //     throw ValidateUtil.validateMsg("该角色名称已重复，请修改后重新提交");
        // }
        //
        // // 更新角色信息
        // TenantRoleDO updateDO = new TenantRoleDO();
        // updateDO.setId(existRole.getId());
        // updateDO.setRoleName(param.getRoleName());
        //
        // // 准备新的菜单权限列表
        // List<String> menuIdList = param.getMenuIdList();
        // List<RoleMenuDO> roleMenuDOList = new ArrayList<>();
        // for (String menuId : menuIdList) {
        //     RoleMenuDO roleMenuDO = new RoleMenuDO();
        //     roleMenuDO.setRoleId(param.getRoleId());
        //     roleMenuDO.setTenantId(tenantId);
        //     roleMenuDO.setMenuId(menuId);
        //     roleMenuDOList.add(roleMenuDO);
        // }
        //
        // // 事务处理
        // transactionTemplate.execute(status -> {
        //     // 更新角色基本信息
        //     tenantRoleDAO.updateById(updateDO);
        //
        //     // 删除原有的角色菜单关联
        //     roleMenuDAO.removeByRoleId(param.getRoleId());
        //
        //     // 新增角色菜单关联
        //     roleMenuDAO.saveBatch(roleMenuDOList);
        //     return Boolean.TRUE;
        // });
    }

    /**
     * 角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @Override
    public RoleDetailQueryResult getRoleDetail(RoleDetailQueryParam param) {

        // 根据角色ID查询角色信息
        TenantRoleDO tenantRoleDO = tenantRoleDAO.getByRoleId(param.getRoleId());
        if (tenantRoleDO == null) {
            throw ValidateUtil.validateMsg("角色不存在");
        }

        // 查询角色关联的菜单ID列表
        List<String> menuIdList = roleMenuDAO.findMenuIdListByRoleId(param.getRoleId());

        // 构建返回结果
        RoleDetailQueryResult result = new RoleDetailQueryResult();
        result.setRoleId(tenantRoleDO.getRoleId());
        result.setRoleName(tenantRoleDO.getRoleName());
        result.setRoleType(tenantRoleDO.getRoleType());
        result.setMenuIdList(CollectionUtil.isNotEmpty(menuIdList) ? menuIdList : new ArrayList<>());

        // 格式化创建时间
        if (tenantRoleDO.getCreateTime() != null) {
            result.setCreateTime(DateUtil.formatDateTime(tenantRoleDO.getCreateTime()));
        }

        return result;
    }

    /**
     * 删除角色
     *
     * @param param 请求参数
     */
    @Override
    public void deleteRole(OperationAdminRoleDeleteParam param) {
        RoleDeleteParam roleDeleteParam = new RoleDeleteParam();
        roleDeleteParam.setRoleId(param.getRoleId());
        roleManagerService.deleteRole(roleDeleteParam);
    }

    /**
     * 校验租户下是否有可用角色
     *
     * @param param 请求参数
     */
    @Override
    public CheckOperationAdminRoleResult checkOperationAdminRole() {
        TenantRoleDO tenantRoleDO = tenantRoleDAO.getByTenantIdAndPlatformType(TenantContextUtil.getTenantId(), TenantContextUtil.getPlatformType().getCode());
        if (tenantRoleDO == null) {
            return new CheckOperationAdminRoleResult(Boolean.FALSE);
        }
        return new CheckOperationAdminRoleResult(Boolean.TRUE);
    }
}
