package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.frt.usercore.common.constants.CommonConstant;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.AccountBindRoleDO;
import com.frt.usercore.dao.entity.BasicMenuDO;
import com.frt.usercore.dao.repository.AccountBindRoleDAO;
import com.frt.usercore.dao.repository.BasicMenuDAO;
import com.frt.usercore.dao.repository.RoleMenuDAO;
import com.frt.usercore.domain.param.operationadmin.menumanager.MenuListQueryParam;
import com.frt.usercore.domain.result.operationadmin.menumanager.MenuListQueryResult;
import com.frt.usercore.domain.result.operationadmin.rolemanager.OperationAdminMenuTreeResult;
import com.frt.usercore.domain.result.rolemanager.RolePermissionTreeResult;
import com.frt.usercore.service.OperationAdminMenuManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 运营后台菜单管理服务实现类
 *
 * <AUTHOR>
 * @version OperationAdminMenuManagerServiceImpl.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationAdminMenuManagerServiceImpl implements OperationAdminMenuManagerService {


    private final AccountBindRoleDAO accountBindRoleDAO;
    private final RoleMenuDAO roleMenuDAO;
    private final BasicMenuDAO basicMenuDAO;

    /**
     * 权限列表
     *
     * @param param 请求参数
     * @return 权限列表
     */
    @Override
    public MenuListQueryResult getMenuList(MenuListQueryParam param) {
        LogUtil.info(log, "OperationAdminMenuManagerServiceImpl.getMenuList >> 接口开始 >> param = {}", JSON.toJSONString(param));
        String userId = param.getUserId();
        AccountBindRoleDO accountBindRoleDO = accountBindRoleDAO.getByUserId(userId);
        if (accountBindRoleDO == null) {
            LogUtil.info(log, "OperationAdminMenuManagerServiceImpl.getMenuList >> 用户未绑定角色 >> userId = {}", userId);
            throw ValidateUtil.validateMsg("用户未绑定角色");
        }
        String roleId = accountBindRoleDO.getRoleId();
        List<String> menuIdList = roleMenuDAO.findMenuIdListByRoleId(roleId);
        if (CollectionUtil.isEmpty(menuIdList) ){
             return new MenuListQueryResult();
        }
        List<BasicMenuDO> basicMenuDOList = basicMenuDAO.selectMenuByMenuIds(menuIdList);
        if (CollectionUtil.isEmpty(basicMenuDOList)) {
            return new MenuListQueryResult();
        }
        List<OperationAdminMenuTreeResult> parentMenuList = buildTree(basicMenuDOList, CommonConstant.ZERO_STR);
        LogUtil.info(log, "OperationAdminMenuManagerServiceImpl.getMenuList >> 接口结束");
        return new MenuListQueryResult(parentMenuList);
    }

    /**
     * 构建树状结构
     * @param basicMenuDOList 查询列表
     * @param parentId 列表中top 节点的parentId
     * @return 树状结构
     */
    private List<OperationAdminMenuTreeResult>  buildTree(List<BasicMenuDO> basicMenuDOList, String parentId) {
        List<OperationAdminMenuTreeResult> list = new ArrayList<>();
        List<BasicMenuDO> topGrant = basicMenuDOList.stream()
                .filter(grantInfoDO -> grantInfoDO.getParentMenuId().equalsIgnoreCase(parentId)).toList();
        for (BasicMenuDO menuDO : topGrant) {
            List<OperationAdminMenuTreeResult> children = buildTree(basicMenuDOList, menuDO.getMenuId());
            OperationAdminMenuTreeResult result = new OperationAdminMenuTreeResult();
            result.setGrantId(menuDO.getMenuId());
            result.setGrantName(menuDO.getMenuName());
            result.setGrantCode(menuDO.getMenuCode());
            result.setGrantType(menuDO.getMenuType());
            result.setChildren(children);
            list.add(result);
        }
        return list;
    }
}
