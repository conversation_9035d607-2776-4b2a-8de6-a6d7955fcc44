/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service;

import com.frt.usercore.domain.param.rolemanager.MerchantMenuPermissionParam;
import com.frt.usercore.domain.param.rolemanager.SingleMerchantMenuPermissionParam;
import com.frt.usercore.domain.result.rolemanager.MerchantCashierAndShopMenuPermissionResult;
import com.frt.usercore.domain.result.rolemanager.MerchantRolePermissionListResult;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version MerchantRolePermissionService.java, v 0.1 2025-08-29 16:08 zhangling
 */
public interface MerchantRolePermissionService {

    /**
     * 获取权限模板
     * @param param
     * @return
     */
    MerchantCashierAndShopMenuPermissionResult getPermissionTemplate(MerchantMenuPermissionParam param);

    /**
     * 获取单权限模板
     * @param param
     * @return
     */
    MerchantRolePermissionListResult getSinglePermissionTemplate(SingleMerchantMenuPermissionParam param);
}