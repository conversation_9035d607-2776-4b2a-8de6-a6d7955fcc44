<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.BasicMenuMapper">
    <select id="selectMenuByUserId" resultType="com.frt.usercore.dao.entity.BasicMenuDO">
        SELECT DISTINCT m.menu_id, m.menu_code, m.menu_name, m.api_path, m.api_description, m.menu_type, m.permission_id, m.is_visible, m.is_enabled, m.is_del, m.create_time, m.update_time, m.created_by, m.updated_by, m.login_port_type
        FROM frt_basic_menu m
                 LEFT JOIN frt_role_menu rm ON m.menu_id = rm.menu_id
                left JOIN frt_tenant_role ftr ON ftr.role_id = rm.role_id
                 LEFT JOIN frt_account_bind_role abr ON abr.role_id = ftr.role_id
        WHERE abr.user_id = #{userId,jdbcType=VARCHAR}
          and ftr.status = 1 and ftr.is_del = 0
          AND m.is_del = 0
          AND rm.is_del = 0
          AND abr.is_del = 0
    </select>

</mapper>
