<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.AccountBindRoleMapper">

    <update id="updateRoleIdByUserId" parameterType="object">
        update frt_account_bind_role
        set role_id = #{roleId,jdbcType=VARCHAR}
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>


    <select id="countRoleLinkAccount" resultType="com.frt.usercore.domain.dto.result.RoleLinkAccountDTO">
        select
        role_id as roleId,
        count(*) as linkedAccountCount
        from frt_account_bind_role
        where is_del = 0
        and role_id in
        <foreach item="item" collection="roleIdList" separator="," open="(" close=")" index="">
            #{item,jdbcType=VARCHAR}
        </foreach>
        group by role_id
    </select>

</mapper>
