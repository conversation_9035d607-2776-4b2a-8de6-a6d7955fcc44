<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.RoleMenuMapper">

    <select id="findMenuIdListByRoleId" resultType="java.lang.String">
        SELECT menu_id FROM frt_role_menu WHERE role_id = #{roleId, jdbcType=VARCHAR} and is_del = 0
    </select>
</mapper>
