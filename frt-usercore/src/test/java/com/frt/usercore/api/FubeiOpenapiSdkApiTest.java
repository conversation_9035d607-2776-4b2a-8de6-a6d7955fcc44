/// *
// * ailike.com
// * Copyright (C) 2022-2025 All Rights Reserved.
// */
//package com.frt.usercore.api;
//
//import cn.hutool.json.JSONUtil;
//import com.alibaba.fastjson.JSON;
//import com.frt.fubeiopenapisdk.apienum.FubeiPayApiDefinitionEnum;
//import com.frt.fubeiopenapisdk.client.FubeiPayApiClient;
//import com.frt.fubeiopenapisdk.client.model.FubeiPayClientConfigModel;
//import com.frt.fubeiopenapisdk.constant.FubeiPayConstant;
//import com.frt.fubeiopenapisdk.request.FubeiPayOrderQueryRequest;
//import com.frt.fubeiopenapisdk.response.FubeiPayBizResponse;
//import com.frt.usercore.domain.param.rolemanager.MerchantMenuPermissionParam;
//import com.frt.usercore.domain.result.rolemanager.MerchantCashierAndShopMenuPermissionResult;
//import com.frt.usercore.service.MerchantRolePermissionService;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
///**
// * <AUTHOR>
// * @version RoleManagerApiTest.java, v 0.1 2025-09-01 09:55 zhangling
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class FubeiOpenapiSdkApiTest {
//
//    private FubeiPayApiClient fubeiPayApiClient;
//
//    @Before
//    public void setUp() {
//        try {
//            FubeiPayClientConfigModel configModel = new FubeiPayClientConfigModel();
//
//            // 网关地址
//            configModel.setApiParentURL("https://shq-api-beta.51fubei.com/gateway/agent");
//            // 编码格式
//            configModel.setCharset(FubeiPayConstant.Http.CHARSET_UTF8);
//            // 超时时间
//            configModel.setTimeout(30000);
//            // 参数校验
//            configModel.setCheckParam(true);
//
//            configModel.setVendorSn("2023091217592276716a");
//            configModel.setAppSecret("b429032e9ae4f0e4de1db9da8d501c77");
//
//            fubeiPayApiClient = new FubeiPayApiClient(configModel);
//            System.out.println("=== 付呗支付SDK - 支付客户端初始化成功 ===");
//        } catch (Exception e) {
//            System.err.println("初始化付呗支付客户端失败: " + e.getMessage());
//            throw new RuntimeException("初始化付呗支付客户端失败", e);
//        }
//    }
//
//    @Test
//    public void testAutoSign() {
//
//        FubeiPayOrderQueryRequest request = new FubeiPayOrderQueryRequest();
//        request.setOrderSn("20250603155943839280");
//        request.setMerchantId("1678059");
//
//        FubeiPayBizResponse execute = fubeiPayApiClient.execute(request, FubeiPayApiDefinitionEnum.ORDER_QUERY);
//        System.err.println(JSONUtil.toJsonStr(execute));
//    }
//}