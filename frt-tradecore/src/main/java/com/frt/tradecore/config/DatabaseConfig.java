/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.tradecore.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version DatabaseConfig.java, v 0.1 2025-08-26 13:33 wangyi
 */
@Configuration
public class DatabaseConfig {

    @Autowired
    private NacosConfig nacosConfig;

    @Bean
    public DruidDataSource dataSource() throws Exception{
        DruidDataSource dataSource = new DruidDataSource();

        dataSource.setDriverClassName(nacosConfig.getDataSourceDriverClassName());
        dataSource.setUrl(nacosConfig.getDataSourceUrl());
        dataSource.setUsername(nacosConfig.getDataSourceUsername());
        dataSource.setPassword(nacosConfig.getDataSourcePassword());

        dataSource.setInitialSize(1);
        dataSource.setMinIdle(1);
        dataSource.setMaxActive(64);
        dataSource.setValidationQuery("SELECT '1'");
        dataSource.setValidationQueryTimeout(60);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        dataSource.setTestWhileIdle(true);
        dataSource.setMaxWait(60000);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setFilters("wall");

        return dataSource;
    }

}