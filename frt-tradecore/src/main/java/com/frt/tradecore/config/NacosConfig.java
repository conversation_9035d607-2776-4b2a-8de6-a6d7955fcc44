/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.tradecore.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version NacosConfig.java, v 0.1 2025-08-26 12:31 wangyi
 */
@Configuration
@RefreshScope
@Getter
public class NacosConfig {

    /**
     * 环境标识
     */
    @Value("${env}")
    private String env;

    /**
     * 数据库地址
     */
    @Value("${data.source.url}")
    private String dataSourceUrl;

    /**
     * 数据库驱动名
     */
    @Value("${data.source.driver.class.name}")
    private String dataSourceDriverClassName;

    /**
     * 数据库连接名
     */
    @Value("${data.source.username}")
    private String dataSourceUsername;

    /**
     * 数据库连接密码
     */
    @Value("${data.source.password}")
    private String dataSourcePassword;
}