package com.frt.tradecore.config.advice;

import com.frt.tradecore.common.enums.exception.SystemErrorEnum;
import com.frt.tradecore.common.exception.base.BaseException;
import com.frt.tradecore.common.utils.LogUtil;
import com.frt.tradecore.domain.result.base.OpenfeignApiResult;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 全局异常处理器和返回结果包装器
 * 统一处理API异常并返回统一格式
 */
@Slf4j
@RestControllerAdvice(basePackages = "com.frt.tradecore.api")
public class ApiGlobalAdviceHandler implements ResponseBodyAdvice<Object> {

    /**
     * 处理BaseException业务异常
     *
     * @param ex BaseException异常
     * @return 统一返回格式
     */
    @ExceptionHandler(BaseException.class)
    public OpenfeignApiResult handleBaseException(BaseException ex) {
        LogUtil.error(log, "tradecore业务异常 >> ", ex);
        return OpenfeignApiResult.fail(ex);
    }

    /**
     * 处理其他未捕获的异常
     *
     * @param ex Exception异常
     * @return 统一返回格式
     */
    @ExceptionHandler(Exception.class)
    public OpenfeignApiResult handleException(Exception ex) {
        LogUtil.error(log, "tradecore全局异常 >> ", ex);
        return OpenfeignApiResult.fail(SystemErrorEnum.SYSTEM_ERROR.exception());
    }

    @Override
    public boolean supports(@NotNull MethodParameter returnType, @NotNull Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    /**
     * 处理正常返回的数据
     *
     * @param body
     * @param returnType
     * @param selectedContentType
     * @param selectedConverterType
     * @param request
     * @param response
     * @return
     */
    @Override
    public Object beforeBodyWrite(Object body, @NotNull MethodParameter returnType, @NotNull MediaType selectedContentType,
                                  @NotNull Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  @NotNull ServerHttpRequest request, @NotNull ServerHttpResponse response) {
        return body;
    }
}