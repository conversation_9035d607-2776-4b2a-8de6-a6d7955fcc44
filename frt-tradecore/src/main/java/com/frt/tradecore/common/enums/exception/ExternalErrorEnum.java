package com.frt.tradecore.common.enums.exception;

import com.frt.tradecore.common.enums.exception.base.ErrorCodeEnum;
import com.frt.tradecore.common.exception.ExternalException;

import static com.frt.tradecore.common.constants.base.BaseConstants.BASE_PACKAGE;

/**
 * 调用外部系统异常-错误码枚举类
 *
 * <AUTHOR>
 * @version ExternalErrorEnum.java, v 0.1 2024-05-27 13:50 wangyi
 */
public enum ExternalErrorEnum {

    REQUEST_FORM_ERROR(ErrorCodeEnum.REQUEST_FORM_ERROR, "构建请求报文失败"),
    MESSAGE_PARSING_ERROR(ErrorCodeEnum.RESPONSE_DATA_ERROR, "返回报文解析失败"),
    ;

    private final ErrorCodeEnum code;
    private final String msg;

    ExternalErrorEnum(ErrorCodeEnum code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public ErrorCodeEnum getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg
     */
    public String getMsg() {
        return msg;
    }

    /**
     * subCode生成
     *
     * @return
     */
    public String subCode() {
        return this.code.getErrorBusinessType().getSubCodePrefix() + "." + this.name();
    }

    /**
     * 异常统一处理
     *
     * @return
     */
    public ExternalException exception() {
        return new ExternalException(this.code.getErrorCode(), this.subCode(), this.msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }

    /**
     * 异常统一处理
     *
     * @param msg 自定义错误信息
     * @return
     */
    public ExternalException exception(String msg) {
        return new ExternalException(this.code.getErrorCode(), this.subCode(), msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }
}