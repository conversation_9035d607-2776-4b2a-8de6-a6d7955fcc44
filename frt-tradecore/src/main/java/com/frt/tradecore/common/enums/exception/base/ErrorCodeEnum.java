package com.frt.tradecore.common.enums.exception.base;

/**
 * <AUTHOR>
 * @version ErrorCodeEnum.java, v 0.1 2024-05-31 17:12 wangyi
 */
public enum ErrorCodeEnum {

    NO_PRIVILEGE("100001", "当前用户无权限", ErrorBusinessTypeEnum.BUSINESS_ERROR),

    INPUT_CORRECT_PHONE("100002", "请输入正确的手机号码", ErrorBusinessTypeEnum.VERIFICATION_ERROR),

    PHONE_NOT_BOUND("100003", "该手机号未绑定账号", ErrorBusinessTypeEnum.VERIFICATION_ERROR),

    PHONE_BOUND_MANY("100004", "手机号绑定多个账号", ErrorBusinessTypeEnum.VERIFICATION_ERROR),

    PARAMETER_VALIDATION_FAILED("200001", "参数校验失败", ErrorBusinessTypeEnum.VERIFICATION_ERROR),

    AUTH_DATA_INVALID("300001", "调用方信息不正确", ErrorBusinessTypeEnum.AUTH_ERROR),

    SYS_ERROR("400001", "系统异常", ErrorBusinessTypeEnum.SYSTEM_ERROR),

    DATABASE_HANDLE_ERROR("500001", "数据操作失败", ErrorBusinessTypeEnum.DATA_ERROR),

    FILE_HANDLE_ERROR("600001", "文件操作失败", ErrorBusinessTypeEnum.RESOURCE_ERROR),
    FILE_DATA_INVALID("600002", "文件不合法", ErrorBusinessTypeEnum.RESOURCE_ERROR),

    REQUEST_FORM_ERROR("700001", "构建请求报文失败", ErrorBusinessTypeEnum.EXTERNAL_ERROR),
    RESPONSE_DATA_ERROR("700002", "返回报文解析失败", ErrorBusinessTypeEnum.EXTERNAL_ERROR),
    REQUEST_APPLY_ERROR("700003", "外部接口请求失败", ErrorBusinessTypeEnum.EXTERNAL_ERROR),    ;

    private final String errorCode;
    private final String desc;
    private final ErrorBusinessTypeEnum errorBusinessType;

    ErrorCodeEnum(String errorCode, String desc, ErrorBusinessTypeEnum errorBusinessType) {
        this.errorCode = errorCode;
        this.desc = desc;
        this.errorBusinessType = errorBusinessType;
    }

    /**
     * Getter method for property <tt>errorCode</tt>.
     *
     * @return property value of errorCode
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of desc
     */
    public String getDesc() {
        return desc;
    }

    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of desc
     */
    public ErrorBusinessTypeEnum getErrorBusinessType() {
        return errorBusinessType;
    }
}