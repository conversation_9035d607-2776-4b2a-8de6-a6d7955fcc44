package com.frt.tradecore.common.enums.exception;

import com.frt.tradecore.common.enums.exception.base.ErrorCodeEnum;
import com.frt.tradecore.common.exception.InternalException;

import static com.frt.tradecore.common.constants.base.BaseConstants.BASE_PACKAGE;

/**
 * 文件存储与缓存错误-错误码枚举类
 *
 * <AUTHOR>
 * @version AuthErrorEnum.java, v 0.1 2024-05-27 13:50 wangyi
 */
public enum ResourceErrorEnum {

    FILE_SIZE_LIMIT(ErrorCodeEnum.FILE_DATA_INVALID, "文件大小超出限制"),
    FILE_TYPE_INVALID(ErrorCodeEnum.FILE_DATA_INVALID, "文件格式不合法"),
    IMAGE_TYPE_INVALID(ErrorCodeEnum.FILE_DATA_INVALID, "图片格式不合法"),
    FILE_NOT_FOUND(ErrorCodeEnum.FILE_DATA_INVALID, "文件资源不存在"),
    FILE_UPLOAD_FAIL(ErrorCodeEnum.FILE_HANDLE_ERROR, "文件上传失败"),
    ;

    private final ErrorCodeEnum code;
    private final String msg;

    ResourceErrorEnum(ErrorCodeEnum code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public ErrorCodeEnum getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg
     */
    public String getMsg() {
        return msg;
    }

    /**
     * subCode生成
     *
     * @return
     */
    public String subCode() {
        return this.code.getErrorBusinessType().getSubCodePrefix() + "." + this.name();
    }

    /**
     * 异常统一处理
     *
     * @return
     */
    public InternalException exception() {
        return new InternalException(this.code.getErrorCode(), this.subCode(), this.msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }

    /**
     * 异常统一处理
     *
     * @param msg 自定义错误信息
     * @return
     */
    public InternalException exception(String msg) {
        return new InternalException(this.code.getErrorCode(), this.subCode(), msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }
}