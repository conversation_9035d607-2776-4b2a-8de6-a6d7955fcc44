package com.frt.tradecore.common.enums.exception.base;

/**
 * <AUTHOR>
 * @version ErrorBusinessTypeEnum.java, v 0.1 2024-05-31 17:12 wangyi
 */
public enum ErrorBusinessTypeEnum {

    RESOURCE_ERROR("文件资源异常", "RESOURCE_ERROR", "T.URE"),
    SYSTEM_ERROR("系统异常", "SYSTEM_ERROR", "T.SYS"),
    VERIFICATION_ERROR("校验异常", "VERIFICATION_ERROR", "T.VE"),
    DATA_ERROR("数据异常", "DATA_ERROR", "T.DATA"),
    AUTH_ERROR("授权异常", "AUTH_ERROR", "T.AE"),
    BUSINESS_ERROR("业务异常", "BUSINESS_ERROR", "T.BE"),
    EXTERNAL_ERROR("调用外部系统异常", "EXTERNAL_ERROR", "T.ER"),
    ;

    private final String businessName;
    private final String businessType;
    private final String subCodePrefix;

    ErrorBusinessTypeEnum(String businessName, String businessType, String subCodePrefix) {
        this.businessName = businessName;
        this.businessType = businessType;
        this.subCodePrefix = subCodePrefix;
    }

    /**
     * Getter method for property <tt>businessName</tt>.
     *
     * @return property value of businessName
     */
    public String getBusinessName() {
        return businessName;
    }

    /**
     * Getter method for property <tt>businessType</tt>.
     *
     * @return property value of businessType
     */
    public String getBusinessType() {
        return businessType;
    }

    /**
     * Getter method for property <tt>subCodePrefix</tt>.
     *
     * @return property value of subCodePrefix
     */
    public String getSubCodePrefix() {
        return subCodePrefix;
    }
}