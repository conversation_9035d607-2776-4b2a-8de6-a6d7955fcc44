/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version ProtocolSignCheckResult.java, v 0.1 2025-08-29 18:58 zhangling
 */
@Data
public class ProtocolSignCheckResult implements Serializable {

    private static final long serialVersionUID = -1116675049646382176L;
    /**
     * 是否已签署
     */
    private boolean sign;

    List<ProtocolInfoResult> protocolList;

    /**
     * 密码校验结果 true-成功 false-失败
     */
    private  Boolean success;

    /**
     * 密码错误次数
     */
    private  Integer passwordErrorCount;

    /**
     * 密码错误最大次数
     */
    private  Integer passwordMaxErrorCount;

    /**
     * 展示锁定时间（分钟）（nacos 获取）
     */
    private  Integer showLockTime;
}