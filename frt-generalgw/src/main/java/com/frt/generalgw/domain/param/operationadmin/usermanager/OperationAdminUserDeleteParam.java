/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.param.operationadmin.usermanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;

/**
 * 员工删除参数
 */
@Data
public class OperationAdminUserDeleteParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 4410310259438116286L;
    /**
     * 员工ID
     */
    @NotBlank(message = "员工ID不能为空")
    private String userId;

}