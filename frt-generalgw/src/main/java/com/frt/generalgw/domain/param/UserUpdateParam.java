/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 员工更新参数
 *
 * <AUTHOR>
 * @version UserUpdateParam.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserUpdateParam implements Serializable {

    private static final long serialVersionUID = -456789012345678901L;

    /**
     * 员工ID
     */
    private String userId;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 员工手机号
     */
    private String phone;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 门店ID
     */
    private List<String> storeIdList;

}