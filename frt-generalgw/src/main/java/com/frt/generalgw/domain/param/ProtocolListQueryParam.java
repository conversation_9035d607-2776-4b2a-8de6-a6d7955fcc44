package com.frt.generalgw.domain.param;
import lombok.Data;

import java.io.Serializable;

/**
 * 协议列表查询参数
 *
 * <AUTHOR>
 * @version ProtocolListQueryParam.java, v 0.1 2025-08-27 13:42 zhangling
 */
@Data
public class ProtocolListQueryParam implements Serializable {

    private static final long serialVersionUID = -481271297905358376L;
    /**
     * 用户名
     */
    private String account;
    /**
     * 终端类型 1-商户端 2-运用端
     */
    private Integer terminalType;
    /**
     * 密码
     */
    private String password;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 验证码
     */
    private String code;
    /**
     * 登录方式 1-密码 2-手机验证码
     */
    private Integer type;
}