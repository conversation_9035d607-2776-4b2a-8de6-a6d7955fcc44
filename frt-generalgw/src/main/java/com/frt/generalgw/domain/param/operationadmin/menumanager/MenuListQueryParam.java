package com.frt.generalgw.domain.param.operationadmin.menumanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 权限列表查询参数
 *
 * <AUTHOR>
 * @version MenuListQueryParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class MenuListQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

}
