package com.frt.generalgw.domain.param.operationadmin.rolemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 修改角色参数
 *
 * <AUTHOR>
 * @version RoleModifyParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class RoleModifyParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @NotBlank(message = "角色ID不能为空")
    private String roleId;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    /**
     * 菜单 id 列表
     */
    @NotNull(message = "菜单列表不能为空")
    private List<String> menuIdList;
}
