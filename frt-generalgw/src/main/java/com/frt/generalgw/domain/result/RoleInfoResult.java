/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.result;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version RoleInfoResult.java, v 0.1 2025-08-27 16:03 zhangling
 */
@Data
public class RoleInfoResult {

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String remark;

    /**
     * 角色模板名称
     */
    private String roleTemplateName;
    /**
     * 关联账号数量
     */
    private Integer linkedAccounts;
}