package com.frt.generalgw.domain.param.operationadmin.rolemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 新增角色参数
 *
 * <AUTHOR>
 * @version RoleAddParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class RoleAddParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    /**
     * 菜单 id 列表
     */
    @NotNull(message = "菜单列表不能为空")
    private List<String> menuIdList;

}
