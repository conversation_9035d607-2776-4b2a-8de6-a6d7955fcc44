package com.frt.generalgw.domain.param.operationadmin.rolemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 角色删除参数
 */
@Data
public class OperationAdminRoleDeleteParam implements Serializable {

	private static final long serialVersionUID = -481271297905358376L;

	/**
	 * 角色ID
	 */
    @NotBlank(message = "角色ID不能为空")
	private String roleId;
}