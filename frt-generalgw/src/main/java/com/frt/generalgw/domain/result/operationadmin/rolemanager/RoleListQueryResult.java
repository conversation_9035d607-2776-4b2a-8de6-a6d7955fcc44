package com.frt.generalgw.domain.result.operationadmin.rolemanager;

import lombok.Data;

import java.io.Serializable;

/**
 * 角色列表查询结果
 *
 * <AUTHOR>
 * @version RoleListQueryResult.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class RoleListQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 关联账号数量
     */
    private Integer linkedAccounts;
}
