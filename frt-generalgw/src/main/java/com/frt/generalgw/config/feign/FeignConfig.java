/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.config.feign;

import cn.hutool.core.util.ObjectUtil;
import feign.Logger;
import com.frt.generalgw.context.LoginContext;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.codec.Decoder;
import feign.jackson.JacksonDecoder;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR>
 * @version FeignConfig.java, v 0.1 2025-08-27 09:38 wangyi
 */
@Configuration
public class FeignConfig implements RequestInterceptor {

    /**
     * 租户ID请求头名称
     */
    public static final String TENANT_ID_HEADER = "TENANT_ID";
    /**
     * 租户ID请求头名称
     */
    public static final String USER_ID_HEADER = "USER_ID";
    /**
     * 租户ID请求头名称
     */
    public static final String IS_ADMIN_HEADER = "IS_ADMIN";
    /**
     * 租户ID请求头名称
     */
    public static final String LOGIN_PORT_HEADER = "LOGIN_PORT";
    /**
     * 租户ID请求头名称
     */
    public static final String PLATFORM_TYPE_HEADER = "PLATFORM_TYPE";
    /**
     * 租户ID请求头名称
     */
    public static final String PLATFORM_ID_HEADER = "PLATFORM_ID";

    /**
     * 发送feign请求前的header处理
     *
     * @param requestTemplate
     */
    @Override
    public void apply(RequestTemplate requestTemplate) {
        HttpServletRequest request = getServletRequest();
        if (null == request) {
            return;
        }
        if (ObjectUtil.isNotNull(LoginContext.getLoginInfo())) {
            requestTemplate.header(TENANT_ID_HEADER, LoginContext.getLoginInfo().getTenantId());
            requestTemplate.header(USER_ID_HEADER, LoginContext.getLoginInfo().getUserId());
            // todo tyw
            if (ObjectUtil.isNotNull(LoginContext.getLoginInfo().getIsAdmin())) {
                requestTemplate.header(IS_ADMIN_HEADER, LoginContext.getLoginInfo().getIsAdmin().toString());
            }
            if (ObjectUtil.isNotNull(LoginContext.getLoginInfo().getLoginPort())) {
                requestTemplate.header(LOGIN_PORT_HEADER, LoginContext.getLoginInfo().getLoginPort().toString());
            }
            if (ObjectUtil.isNotNull(LoginContext.getLoginInfo().getPlatformType())) {
                requestTemplate.header(PLATFORM_TYPE_HEADER, LoginContext.getLoginInfo().getPlatformType().toString());
            }

            requestTemplate.header(PLATFORM_ID_HEADER, LoginContext.getLoginInfo().getPlatformId());
        }
    }

    private HttpServletRequest getServletRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    @Bean
    public FeignDecoder decoder() {
        return new FeignDecoder(new JacksonDecoder());
    }


    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;  // 设置全局日志级别
    }

    @Bean
    public Logger feignLogger() {
        return new FeignLogger();
    }

}