/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.protocol;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.param.ProtocolListQueryParam;
import com.frt.generalgw.domain.param.ProtocolQueryParam;
import com.frt.generalgw.domain.param.merchantmina.auth.MerchantMinaChangePasswordParam;
import com.frt.generalgw.domain.result.ProtocolInfoResult;
import com.frt.generalgw.domain.result.ProtocolSignCheckResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version ProtocolClient.java, v 0.1 2025-08-27 13:59 zhangling
 */
@FeignClient(
        value = "${feign.usercore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface ProtocolClient {

    /**
     * 查询协议签署列表
     *
     * @return
     */
    @PostMapping("/api/protocol/query/protocol-list")
    ProtocolSignCheckResult findProtocolList(@RequestBody ProtocolListQueryParam param);

    /**
     * 查询协议签署内容
     *
     * @return
     */
    @PostMapping("/api/protocol/get/protocol-info")
    ProtocolInfoResult getProtocolInfo(@RequestBody ProtocolQueryParam param);

    @PostMapping("/api/protocol/change/password")
    ResponseEntity<Void> changePassword(@RequestBody MerchantMinaChangePasswordParam param);
}