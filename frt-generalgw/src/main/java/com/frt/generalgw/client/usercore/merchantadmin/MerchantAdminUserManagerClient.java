/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantadmin;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.entity.UserInfo;
import com.frt.generalgw.domain.param.*;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.result.MerchantUserAccountCheckResult;
import com.frt.generalgw.domain.result.UserDetailQueryResult;
import com.frt.generalgw.domain.result.UserStoreListResult;
import com.frt.generalgw.domain.result.common.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 员工管理客户端
 *
 * <AUTHOR>
 * @version UserManagerClient.java, v 0.1 2025-08-27 16:51 zhangling
 */
@FeignClient(
        value = "${feign.usercore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface MerchantAdminUserManagerClient {

    /**
     * 查询员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @PostMapping("/api/admin/user/query-user-list")
    PageResult<UserInfo> getUserList(@RequestBody PageParam<UserListQueryParam> param);

    /**
     * 查询员工详情
     *
     * @param param 请求参数
     * @return 员工详情
     */
    @PostMapping("/api/admin/user/get-user-detail")
    UserDetailQueryResult getUserDetail(@RequestBody UserDetailQueryParam param);

    /**
     * 添加员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/admin/user/add-user")
    ResponseEntity<Void> addUser(@RequestBody UserAddParam param);

    /**
     * 更新员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/admin/user/update-user")
    ResponseEntity<Void> updateUser(@RequestBody UserUpdateParam param);

    /**
     * 删除员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/admin/user/delete-user")
    ResponseEntity<Void> deleteUser(@RequestBody UserDeleteParam param);

    /**
     * 禁用/启用员工
     * @param param
     * @return
     */
    @PostMapping("/api/admin/user/disable-and-enable-user")
    ResponseEntity<Void> disableAndEnableUser(@RequestBody UserDisableAndEnableParam param);

    /**
     * 校验员工账号
     * @param param
     * @return
     */
    @PostMapping("/api/admin/user/check-user-account")
    MerchantUserAccountCheckResult checkUserAccount(@RequestBody MerchantUserAccountCheckParam param);

    /**
     * 修改密码
     */
    @PostMapping("/api/admin/user/update-password")
    ResponseEntity<Void> updatePassword(@RequestBody UpdatePasswordParam param);

    /**
     * 查询员工门店列表
     * @param param
     * @return
     */
    @PostMapping("/api/admin/user/query-user-store-list")
    PageResult<UserStoreListResult> queryUserStoreList(@RequestBody PageParam<UserStoreListParam> param);

}