/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.datacore.test;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.result.common.TestResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @version TestClient.java, v 0.1 2025-08-26 20:07 wangyi
 */
@FeignClient(
        value = "${feign.datacore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface DatacoreTestClient {

    /**
     * 测试请求
     *
     * @return
     */
    @GetMapping("/api/test/hello")
    TestResult hello();

    /**
     * 测试请求
     *
     * @return
     */
    @GetMapping("/api/test/exception")
    TestResult exception();

    /**
     * 测试请求
     *
     * @return
     */
    @GetMapping("/api/test/void")
    ResponseEntity<Void> voidTest();
}