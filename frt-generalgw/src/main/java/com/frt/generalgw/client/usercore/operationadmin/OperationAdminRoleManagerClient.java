package com.frt.generalgw.client.usercore.operationadmin;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.param.RoleDeleteParam;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.operationadmin.rolemanager.CheckOperationAdminRoleParam;
import com.frt.generalgw.domain.param.operationadmin.rolemanager.OperationAdminRoleDeleteParam;
import com.frt.generalgw.domain.param.operationadmin.rolemanager.RoleAddParam;
import com.frt.generalgw.domain.param.operationadmin.rolemanager.RoleDetailQueryParam;
import com.frt.generalgw.domain.param.operationadmin.rolemanager.RoleListQueryParam;
import com.frt.generalgw.domain.param.operationadmin.rolemanager.RoleModifyParam;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.operationadmin.rolemanager.CheckOperationAdminRoleResult;
import com.frt.generalgw.domain.result.operationadmin.rolemanager.RoleDetailQueryResult;
import com.frt.generalgw.domain.result.operationadmin.rolemanager.RoleListQueryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 运营后台角色管理客户端
 *
 * <AUTHOR>
 * @version OperationAdminRoleManagerClient.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@FeignClient(
        value = "${feign.usercore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface OperationAdminRoleManagerClient {

    /**
     * 角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @PostMapping("/api/operation/admin/role/role-list")
    PageResult<RoleListQueryResult> getRoleList(@RequestBody PageParam<RoleListQueryParam> param);

    /**
     * 新增角色
     *
     * @param param 请求参数
     */
    @PostMapping("/api/operation/admin/role/role-add")
    ResponseEntity<Void> addRole(@RequestBody RoleAddParam param);

    /**
     * 修改角色
     *
     * @param param 请求参数
     */
    @PostMapping("/api/operation/admin/role/role-modify")
    ResponseEntity<Void> modifyRole(@RequestBody RoleModifyParam param);

    /**
     * 角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @PostMapping("/api/operation/admin/role/role-detail")
    RoleDetailQueryResult getRoleDetail(@RequestBody RoleDetailQueryParam param);

    /**
     * 删除角色
     *
     * @param param 请求参数
     */
    @PostMapping("/api/operation/admin/role/role-delete")
    ResponseEntity<Void> deleteRole(OperationAdminRoleDeleteParam param);

    /**
     * 校验租户下是否有可用角色
     *
     * @param param 请求参数
     */
    @PostMapping("/api/operation/admin/role/check-role")
    CheckOperationAdminRoleResult checkOperationAdminRole();
}
