package com.frt.generalgw.client.usercore.operationadmin;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminChangePasswordParam;
import com.frt.generalgw.domain.param.operationadmin.auth.*;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminLoginResult;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminResourceResult;
import com.frt.generalgw.domain.result.operationadmin.auth.QueryAccountResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 运营后台权限客户端
 */
@FeignClient(
        value = "${feign.usercore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface OperationAdminClient {

    /**
     * 4.1 登录页资源获取接口
     * 接口名称：operation/web/search/resource
     * 请求方式：POST
     *
     * @param param 请求参数 webAddress 二级域名
     * @return 资源信息
     */
    @PostMapping("/operation/web/search/resource")
    OperationAdminResourceResult searchResource(@RequestBody OperationAdminResourceParam param);

    /**
     * 4.2 发送验证码
     * 接口名称：operation/web/send/code
     * 请求方式：POST
     *
     * @param param 请求参数
     *              tenantId 租户id
     *              phone 手机号
     *              type 场景类型 1-登录 2-修改密码
     */
    @PostMapping("/operation/web/send/code")
    ResponseEntity<Void> sendCode(@RequestBody OperationAdminSendCodeParam param);

    /**
     * 4.3 账号登录
     * 接口名称：operation/web/login
     * 请求方式：POST
     *
     * @param param 登录参数
     *              webAddress 二级域名
     *              tenantId 租户Id
     *              type 登录方式 1-密码登录 2-验证码登录
     *              account 账号
     *              password 密码（md5加密）
     *              code 验证码
     * @return 登录结果
     */
    @PostMapping("/operation/web/login")
    OperationAdminLoginResult login(@RequestBody OperationAdminLoginParam param);

    /**
     * 4.7 账号登出
     * 接口名称：operation/web/logout
     * 请求方式：POST
     */
    @PostMapping("/operation/web/logout")
    ResponseEntity<Void> logout();

    /**
     * 根据账号名称查询账号信息
     *
     * @param param 账号名称
     * @return 账号信息
     */
    @PostMapping("/operation/web/query-account")
    QueryAccountResult queryAccount(@RequestBody QueryAccountParam param);

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/operation/web/send-sms")
    ResponseEntity<Void> sendSms(@RequestBody SendSmsParam param);

    /**
     * 校验短信验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/operation/web/check-sms-code")
    ResponseEntity<Void> checkSmsCode(@RequestBody CheckSmsCodeParam param);

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @PostMapping("/operation/web/update-password")
    ResponseEntity<Void> updatePassword(@RequestBody UpdatePasswordParam param);

    /**
     * 通过老密码设置新密码
     *
     * @param param 修改密码参数
     * @return 修改结果
     */
    @PostMapping("/operation/web/change/password")
    ResponseEntity<Void> changePassword(@RequestBody OperationAdminChangePasswordParam param);
}