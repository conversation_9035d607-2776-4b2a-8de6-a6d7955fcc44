/**
 * <AUTHOR>
 * @date 2025/8/27 14:16
 * @version 1.0 MerchantAuthController
 */
package com.frt.generalgw.controller.merchantadmin;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantAuthController.java, v 0.1 2025-08-27 14:16 tuyuwei
 */

import cn.dev33.satoken.stp.StpUtil;
import com.frt.generalgw.client.usercore.merchantadmin.MerchantAdminClient;
import com.frt.generalgw.client.usercore.protocol.ProtocolClient;
import com.frt.generalgw.common.enums.PlatformEnum;
import com.frt.generalgw.context.LoginContext;
import com.frt.generalgw.domain.mapper.MerchantAdminAuthControllerObjMapper;
import com.frt.generalgw.domain.param.ProtocolListQueryParam;
import com.frt.generalgw.domain.param.ProtocolQueryParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminChangePasswordParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminLoginParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminResetPasswordParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminResourceParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.param.merchantmina.auth.MerchantMinaChangePasswordParam;
import com.frt.generalgw.domain.result.ProtocolInfoResult;
import com.frt.generalgw.domain.result.ProtocolSignCheckResult;
import com.frt.generalgw.domain.result.common.BaseResult;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminLoginResult;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminResourceResult;
import com.frt.generalgw.domain.result.merchantadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.merchantadmin.forgotpassword.GetVerifyCodeResult;
import com.frt.generalgw.service.common.VerifyCodeService;
import com.frt.generalgw.service.operationadmin.OperationAdminAuthService;
import com.frt.generalgw.service.merchantAdmin.MerchantAdminAuthService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/MerchantAdminAuthController
 */
@RestController
@RequestMapping("/merchant/web/auth")
public class MerchantAdminAuthController {

    @Autowired
    private MerchantAdminClient merchantAdminClient;
    @Autowired
    private VerifyCodeService verifyCodeService;
    @Autowired
    private OperationAdminAuthService operationAdminAuthService;
    @Autowired
    private MerchantAdminAuthControllerObjMapper mapper;

    @Autowired
    private MerchantAdminAuthService merchantAdminAuthService;

    @Autowired
    private ProtocolClient protocolClient;

    /**
     * 商户后台登录页资源获取接口
     *
     * @param param 请求参数
     * @return 资源信息
     * @Response {@link BaseResult<MerchantAdminResourceResult>}
     */
    @PostMapping("/search/resource")
    public BaseResult<MerchantAdminResourceResult> searchResource(@RequestBody MerchantAdminResourceParam param) {
        return BaseResult.success(merchantAdminAuthService.searchResource(param));
    }

    /**
     * 3.1.3 账号登录
     *
     * @param param 登录参数
     * @return 登录结果
     */
    @PostMapping("/login")
    public BaseResult<MerchantAdminLoginResult> login(@RequestBody MerchantAdminLoginParam param) {
        return BaseResult.success(merchantAdminClient.login(param));
    }

    /**
     * 3.1.7 账号登出
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    public BaseResult logout() {
        StpUtil.logoutByTokenValue(LoginContext.getToken());
        merchantAdminClient.logout();
        return BaseResult.success();
    }

    // ========== 忘记密码相关接口 ==========


    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    @PostMapping("/get-verify-code")
    public BaseResult<GetVerifyCodeResult> getVerifyCode(@Validated @RequestBody GetVerifyCodeParam param) {
        return BaseResult.success(new GetVerifyCodeResult(verifyCodeService.getVerifyCode().getUrl()));
    }

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    @PostMapping("/check-verify-code")
    public BaseResult<CheckVerifyCodeResult> checkVerifyCode(@Validated @RequestBody CheckVerifyCodeParam param) {
        // TODO: 临时注释，等待解决Lombok编译问题后恢复
        return BaseResult.success(mapper.toCheckVerifyCodeResult(operationAdminAuthService.checkVerifyCode(mapper.toOperationAdminCheckVerifyCodeParam(param))));
    }

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/sen-sms")
    public BaseResult sendSms(@Validated @RequestBody SendSmsParam param) {
        merchantAdminClient.sendSms(param);
        return BaseResult.success();
    }

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public BaseResult checkSmsCode(@Validated @RequestBody CheckSmsCodeParam param) {
        merchantAdminClient.checkSmsCode(param);
        return BaseResult.success();
    }

    /**
     * 获取协议列表
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-list")
    public BaseResult<ProtocolSignCheckResult> getProtocolList(@RequestBody ProtocolListQueryParam param) {
        // 实际实现中，这里应该调用服务层获取协议列表数据
        // 目前仅返回一个空的结果对象
        param.setTerminalType(PlatformEnum.MERCHANT.getCode());
        return BaseResult.success(protocolClient.findProtocolList(param));
    }

    /**
     * 获取协议详情
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-info")
    public BaseResult<ProtocolInfoResult> getProtocolInfo(@RequestBody ProtocolQueryParam param) {
        // 实际实现中，这里应该调用服务层获取协议列表数据
        // 目前仅返回一个空的结果对象
        param.setTerminalType(PlatformEnum.MERCHANT.getCode());
        return BaseResult.success(protocolClient.getProtocolInfo(param));
    }


}