/**
 * <AUTHOR>
 * @date 2025/8/27 14:43
 * @version 1.0 MerchantMinaController
 */
package com.frt.generalgw.controller.merchantmina;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantMinaController.java, v 0.1 2025-08-27 14:43 tuyuwei
 */



import cn.dev33.satoken.stp.StpUtil;
import com.frt.generalgw.client.usercore.merchantmina.MerchantMinaClient;
import com.frt.generalgw.client.usercore.protocol.ProtocolClient;
import com.frt.generalgw.common.enums.PlatformEnum;
import com.frt.generalgw.context.LoginContext;
import com.frt.generalgw.domain.mapper.MerchantMinaAuthControllerObjMapper;
import com.frt.generalgw.domain.param.ProtocolListQueryParam;
import com.frt.generalgw.domain.param.ProtocolQueryParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminResetPasswordParam;
import com.frt.generalgw.domain.param.merchantmina.auth.*;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.UpdatePasswordParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeParam;
import com.frt.generalgw.domain.result.ProtocolInfoResult;
import com.frt.generalgw.domain.result.ProtocolSignCheckResult;
import com.frt.generalgw.domain.result.common.BaseResult;
import com.frt.generalgw.domain.result.common.LoginResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaLoginResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaResourceResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaSearchPhoneResult;
import com.frt.generalgw.domain.result.merchantmina.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.merchantmina.forgotpassword.GetVerifyCodeResult;

import com.frt.generalgw.service.merchantMina.MerchantMinaAuthService;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.VerifyCodeResult;
import com.frt.generalgw.service.common.VerifyCodeService;
import com.frt.generalgw.service.operationadmin.OperationAdminAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版小程序/MerchantMinaAuthController
 */
@RestController
@RequestMapping("/merchant/mina/auth")
@Slf4j
public class MerchantMinaAuthController {

    @Autowired
    private MerchantMinaClient merchantMinaClient;
	@Autowired
	private VerifyCodeService verifyCodeService;
	@Autowired
	private OperationAdminAuthService operationAdminAuthService;
   @Autowired
   private MerchantMinaAuthControllerObjMapper mapper;

    @Autowired
    private MerchantMinaAuthService merchantMinaAuthService;

    @Autowired
    private ProtocolClient protocolClient;

    /**
     * 商户小程序登录页资源获取接口
     * @param param
     * @return
     */
    @PostMapping("/search/resource")
    public BaseResult<MerchantMinaResourceResult> searchResource(@RequestBody MerchantMinaResourceParam param) {
        return BaseResult.success(merchantMinaAuthService.searchResource(param));
    }

    /**
     * 商户小程序登录
     * @param param
     * @return
     */
    @PostMapping("/login")
    public BaseResult<MerchantMinaLoginResult> login(@RequestBody MerchantMinaLoginParam param) {
        return BaseResult.success(merchantMinaClient.login(param));
    }

    /**
     * 商户小程序登出
     * @return
     */
    @PostMapping("/logout")
    public BaseResult logout() {
        StpUtil.logoutByTokenValue(LoginContext.getToken());
        merchantMinaClient.logout();
        return BaseResult.success();
    }

    // ========== 忘记密码相关接口 ==========


    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    @PostMapping("/get-verify-code")
    public BaseResult<GetVerifyCodeResult> getVerifyCode(@Validated @RequestBody GetVerifyCodeParam param) {
        return BaseResult.success(new GetVerifyCodeResult(verifyCodeService.getVerifyCode().getUrl()));
    }

   /**
    * 校验图文验证码
    *
    * @param param 请求参数
    * @return 校验结果
    */
   @PostMapping("/check-verify-code")
   public BaseResult<CheckVerifyCodeResult> checkVerifyCode(@Validated @RequestBody CheckVerifyCodeParam param) {
       return BaseResult.success(mapper.toCheckVerifyCodeResult(operationAdminAuthService.checkVerifyCode(mapper.toOperationAdminCheckVerifyCodeParam(param))));
   }

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/sen-sms")
    public BaseResult sendSms(@Validated @RequestBody SendSmsParam param) {
        merchantMinaClient.sendSms(param);
        return BaseResult.success();
    }

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public BaseResult checkSmsCode(@Validated @RequestBody CheckSmsCodeParam param) {
        merchantMinaClient.checkSmsCode(param);
        return BaseResult.success();
    }

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @PostMapping("/update-password")
    public BaseResult updatePassword(@Validated @RequestBody UpdatePasswordParam param) {
        merchantMinaClient.updatePassword(param);
        return BaseResult.success();
    }

    /**
     * 获取协议列表
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-list")
    public BaseResult<ProtocolSignCheckResult> getProtocolList(@RequestBody ProtocolListQueryParam param) {
        param.setTerminalType(PlatformEnum.MERCHANT.getCode());
        return BaseResult.success(protocolClient.findProtocolList(param));
    }

    /**
     * 获取协议详情
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-info")
    public BaseResult<ProtocolInfoResult> getProtocolInfo(@RequestBody ProtocolQueryParam param) {
        param.setTerminalType(PlatformEnum.MERCHANT.getCode());
        return BaseResult.success(protocolClient.getProtocolInfo(param));
    }

    /**
     * 小程序验证码登录——获取验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/login-send-sms")
    public BaseResult sendLoginSms(@RequestBody MerchantMinaLoginSendSmsParam param) {
        merchantMinaClient.sendLoginSms(param);
        return BaseResult.success();
    }

}