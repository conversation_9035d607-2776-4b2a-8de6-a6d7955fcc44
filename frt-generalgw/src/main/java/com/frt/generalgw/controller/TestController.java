/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller;

import com.frt.generalgw.client.datacore.test.DatacoreTestClient;
import com.frt.generalgw.client.usercore.test.TestClient;
import com.frt.generalgw.domain.param.common.BaseParam;
import com.frt.generalgw.domain.result.common.TestResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version TestController.java, v 0.1 2025-08-26 20:08 wangyi
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private TestClient testClient;
    @Autowired
    private DatacoreTestClient datacoreTestClient;

    @GetMapping("/usercore/consumer")
    public TestResult consumer(BaseParam param) {
        return testClient.hello();
    }

    @GetMapping("/usercore/exception")
    public TestResult exception(BaseParam param) {
        return testClient.exception();
    }

    @GetMapping("/usercore/void")
    public void voidTest(BaseParam param) {
        testClient.voidTest();
    }

    @GetMapping("/datacore/consumer")
    public TestResult datacoreConsumer(BaseParam param) {
        return datacoreTestClient.hello();
    }
}