/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantmina;

import com.frt.generalgw.client.usercore.merchantadmin.RoleManagerClient;
import com.frt.generalgw.common.enums.PlatformEnum;
import com.frt.generalgw.domain.entity.RoleInfo;
import com.frt.generalgw.domain.param.*;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.result.*;
import com.frt.generalgw.domain.result.common.BaseResult;
import com.frt.generalgw.domain.result.common.ListResult;
import com.frt.generalgw.domain.result.common.PageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版小程序/MerchantMinaRoleManagerController
 *
 * <AUTHOR>
 * @version RoleManagerController.java, v 0.1 2025-08-27 14:52 zhangling
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/merchant/mina/role")
public class MerchantMinaRoleManagerController {

    private final RoleManagerClient roleManagerClient;

    /**
     * 查询角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @PostMapping("/role-list")
    public BaseResult<PageResult<RoleInfoResult>> getRoleList(@RequestBody PageParam<RoleListQueryParam> param) {
        final RoleListQueryParam query = param.getQuery();
        query.setTerminalType(PlatformEnum.MERCHANT.getCode());
        param.setQuery(query);
        return BaseResult.success(roleManagerClient.getRoleList(param));
    }

    /**
     * 查询角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @PostMapping("/role-detail")
    public BaseResult<RoleDetailQueryResult> getRoleDetail(@RequestBody RoleDetailQueryParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        return BaseResult.success(roleManagerClient.getRoleDetail(param));
    }

    /**
     * 添加角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/role-add")
    public BaseResult addRole(@RequestBody RoleAddParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        roleManagerClient.addRole(param);

        return BaseResult.success();
    }

    /**
     * 更新角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/role-update")
    public BaseResult updateRole(@RequestBody RoleUpdateParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        roleManagerClient.updateRole(param);

        return BaseResult.success();
    }

    /**
     * 删除角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/role-delete")
    public BaseResult deleteRole(@RequestBody RoleDeleteParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        roleManagerClient.deleteRole(param);

        return BaseResult.success();
    }

    /**
     * 获取权限模版
     */
    @PostMapping("/get-single-permission-template")
    public BaseResult<MerchantRolePermissionListResult> getPermissionTemplate(@RequestBody SingleMerchantMenuPermissionParam param) {
        return BaseResult.success(roleManagerClient.getSinglePermissionTemplate(param));
    }

    /**
     * 获取权限菜单模板
     * @param param
     * @return
     */
    @PostMapping("/role-template-list")
    public BaseResult<ListResult<RoleTemplateInfoResult>> getRoleTemplateList(@RequestBody MerchantRoleTemplateParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        return BaseResult.success(roleManagerClient.getRoleTemplateList(param));
    }

    /**
     * 角色名称检查
     * @param param
     * @return
     */
    @PostMapping("/role-name-check")
    public BaseResult<RoleNameCheckResult> checkRoleName(@RequestBody RoleNameCheckParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        return BaseResult.success(roleManagerClient.checkRoleName(param));
    }


}