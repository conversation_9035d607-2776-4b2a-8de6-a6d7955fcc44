package com.frt.generalgw.interceptor;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaFoxUtil;
import com.alibaba.fastjson.JSON;
import com.frt.generalgw.common.enums.PlatformEnum;
import com.frt.generalgw.common.enums.exception.AuthErrorEnum;
import com.frt.generalgw.context.LoginContext;
import com.frt.generalgw.domain.result.common.LoginResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;



public class TokenInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取token
        String token = request.getHeader("Access-Token");
        // token校验
        if (SaFoxUtil.isNotEmpty(token)) {
                SaSession session = StpUtil.getTokenSessionByToken(token);
                if (session != null) {
                    // 在TokenInterceptor中读取
                    String loginInfoJson = (String) session.get(token);
                    LoginResult loginInfo = JSON.parseObject(loginInfoJson, LoginResult.class);
                    if (loginInfo != null) {
                        // 设置登录上下文
                        LoginContext.setLoginInfo(loginInfo);
                        return true;
                } else {
                    throw AuthErrorEnum.LOGIN_SESSION_ERROR.exception();
                }
                } else {
                    throw AuthErrorEnum.LOGIN_SESSION_ERROR.exception();
                }
            }

        // token无效或不存在，返回未登录错误
        throw AuthErrorEnum.GET_TOKEN_ERROR.exception();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除登录上下文
        LoginContext.clear();
    }
}
