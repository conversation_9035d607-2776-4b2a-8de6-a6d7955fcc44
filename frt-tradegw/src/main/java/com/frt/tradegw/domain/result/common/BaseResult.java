/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.tradegw.domain.result.common;

import com.frt.tradegw.common.exception.base.BaseException;
import lombok.Data;

/**
 * <AUTHOR>
 * @version BaseApiResult.java, v 0.1 2025-08-27 13:51 wangyi
 */
@Data
public class BaseResult<T> {

    /**
     * success
     */
    private boolean success;

    /**
     * 状态码
     */
    private String code;

    /**
     * 子状态码
     */
    private String subCode;

    /**
     * 返回信息
     */
    private String message;

    /**
     * 子返回信息
     */
    private String subMessage;

    /**
     * data
     */
    private T data;

    public static <T> BaseResult<T> success(T data) {
        BaseResult<T> baseResult = new BaseResult<>();
        baseResult.setMessage("success");
        baseResult.setSuccess(Boolean.TRUE);
        if (null != data) {
            baseResult.setData(data);
        }
        return baseResult;
    }

    public static BaseResult success() {
        BaseResult baseResult = new BaseResult<>();
        baseResult.setMessage("success");
        baseResult.setSuccess(Boolean.TRUE);
        return baseResult;
    }


    public void fail(String code, String msg, String subCode) {
        this.success = Boolean.FALSE;
        this.setCode(code);
        this.setMessage(msg);
        this.setSubCode(subCode);
    }

    public void fail(BaseException exception) {
        this.success = Boolean.FALSE;
        this.setCode(exception.getCode());
        this.setMessage(exception.getMsg());
        this.setSubCode(exception.getSubCode());
    }
}