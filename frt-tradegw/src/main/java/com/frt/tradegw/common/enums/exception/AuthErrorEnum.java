package com.frt.tradegw.common.enums.exception;

import com.frt.tradegw.common.enums.exception.base.ErrorCodeEnum;
import com.frt.tradegw.common.exception.InternalException;

import static com.frt.tradegw.common.constants.BaseConstants.BASE_PACKAGE;


/**
 * 认证与授权错误-错误码枚举类
 *
 * <AUTHOR>
 * @version AuthErrorEnum.java, v 0.1 2024-05-27 13:50 wangyi
 */
public enum AuthErrorEnum {

    SIGN_INVALID(ErrorCodeEnum.AUTH_DATA_INVALID, "签名错误"),
    ACCOUNT_NOT_EXIST(ErrorCodeEnum.AUTH_DATA_INVALID, "账号不存在"),

    ACCOUNT_NOT_ACTIVE(ErrorCodeEnum.AUTH_DATA_INVALID, "账号已禁用"),
    DOMAIN_NAME_EXIST(ErrorCodeEnum.AUTH_DATA_INVALID, "域名不存在"),

    ROLE_NOT_EXIST(ErrorCodeEnum.AUTH_DATA_INVALID, "角色不存在"),

    PASSWORD_CHECK_ERROR(ErrorCodeEnum.AUTH_DATA_INVALID, "密码校验异常"),

    RESOURCE_NOT_EXIST(ErrorCodeEnum.AUTH_DATA_INVALID, "资源不存在"),

    PROTOCOL_SIGN_ERROR(ErrorCodeEnum.AUTH_DATA_INVALID,"协议签署异常"),

    LOGIN_ERROR(ErrorCodeEnum.AUTH_DATA_INVALID, "登录异常"),
    LOGIN_SESSION_ERROR(ErrorCodeEnum.AUTH_DATA_INVALID, "获取登录态异常"),
    GET_TOKEN_ERROR(ErrorCodeEnum.AUTH_DATA_INVALID, "获取token异常"),

    ;

    private final ErrorCodeEnum code;
    private final String msg;

    AuthErrorEnum(ErrorCodeEnum code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public ErrorCodeEnum getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg
     */
    public String getMsg() {
        return msg;
    }

    /**
     * subCode生成
     *
     * @return
     */
    public String subCode() {
        return this.code.getErrorBusinessType().getSubCodePrefix() + "." + this.name();
    }

    /**
     * 异常统一处理
     *
     * @return
     */
    public InternalException exception() {
        return new InternalException(this.code.getErrorCode(), this.subCode(), this.msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }

    /**
     * 异常统一处理
     *
     * @param msg 自定义错误信息
     * @return
     */
    public InternalException exception(String msg) {
        return new InternalException(this.code.getErrorCode(), this.subCode(), msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }
}