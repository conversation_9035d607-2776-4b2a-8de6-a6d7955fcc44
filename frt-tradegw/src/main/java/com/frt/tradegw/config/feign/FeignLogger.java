/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.tradegw.config.feign;

import com.frt.tradegw.util.LogUtil;
import feign.Logger;
import feign.Response;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * 自定义Feign日志记录器
 */
@Slf4j
public class FeignLogger extends Logger {

    @Override
    protected void log(String configKey, String format, Object... args) {
        // 记录Feign调用日志
        LogUtil.info(log, "Feign调用 >> param={}", String.format(methodTag(configKey) + format, args));
    }

    @Override
    protected void logRetry(String configKey, Level logLevel) {
        LogUtil.info(log, "Feign调用重试 >> " , methodTag(configKey));
    }

    @Override
    protected void logRequest(String configKey, Level logLevel, feign.Request request) {
        super.logRequest(configKey, logLevel, request);
        LogUtil.info(log,"Feign请求 >> configKey: {}, method: {}, url: {}, headers: {}",
                configKey, request.method(), request.url(), request.headers());
    }

    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime) throws IOException {
        Response resp = super.logAndRebufferResponse(configKey, logLevel, response, elapsedTime);
        LogUtil.info(log,"Feign响应 >> configKey: {}, status: {}, reason: {}, elapsedTime: {}ms",
                configKey, resp.status(), resp.reason(), elapsedTime);
        return resp;
    }

    @Override
    protected IOException logIOException(String configKey, Level logLevel, IOException ioe, long elapsedTime) {
        LogUtil.error(log,"Feign IO异常 >> configKey: {}, elapsedTime: {}ms, error: ", configKey, elapsedTime, ioe);
        return super.logIOException(configKey, logLevel, ioe, elapsedTime);
    }
}