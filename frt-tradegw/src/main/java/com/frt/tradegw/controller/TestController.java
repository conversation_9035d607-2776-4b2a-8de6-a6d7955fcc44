/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.tradegw.controller;

import com.frt.tradegw.client.tradecore.test.TradecoreTestClient;
import com.frt.tradegw.domain.param.common.BaseParam;
import com.frt.tradegw.domain.result.common.TestResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version TestController.java, v 0.1 2025-08-26 20:08 wangyi
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private TradecoreTestClient tradecoreTestClient;
    @GetMapping("/tradecore/consumer")
    public TestResult consumer(BaseParam param) {
        return tradecoreTestClient.hello();
    }

    @GetMapping("/tradecore/exception")
    public void exception(BaseParam param) {
       tradecoreTestClient.exception();
    }
}